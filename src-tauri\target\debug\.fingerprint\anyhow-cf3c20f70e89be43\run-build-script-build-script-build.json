{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 2886658769257854125]], "local": [{"RerunIfChanged": {"output": "debug\\build\\anyhow-cf3c20f70e89be43\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}