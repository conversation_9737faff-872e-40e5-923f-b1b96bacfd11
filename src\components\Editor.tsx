import React, { useState, useEffect, useRef } from 'react'
import { Note, ColorTheme, AppSettings } from '../types'
import SyntaxHighlighter from './SyntaxHighlighter'

interface EditorProps {
  note: Note
  onUpdateNote: (id: string, updates: Partial<Note>) => void
  theme: ColorTheme
  settings: AppSettings
  isFullscreen: boolean
  onToggleFullscreen: () => void
}

const Editor: React.FC<EditorProps> = ({
  note,
  onUpdateNote,
  theme,
  settings,
  isFullscreen,
  onToggleFullscreen
}) => {
  const [content, setContent] = useState(note.content)
  const [title, setTitle] = useState(note.title)
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [wordCount, setWordCount] = useState(0)
  const [charCount, setCharCount] = useState(0)
  const [showPreview, setShowPreview] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const titleInputRef = useRef<HTMLInputElement>(null)
  const editorRef = useRef<HTMLDivElement>(null)

  // 当切换笔记时更新内容
  useEffect(() => {
    setContent(note.content)
    setTitle(note.title)
  }, [note.id, note.content, note.title])

  // 计算字数和字符数
  useEffect(() => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0).length
    const chars = content.length
    setWordCount(words)
    setCharCount(chars)
  }, [content])

  // 自动保存内容
  useEffect(() => {
    const timer = setTimeout(() => {
      if (content !== note.content) {
        onUpdateNote(note.id, { content })
      }
    }, 1000) // 1秒后自动保存

    return () => clearTimeout(timer)
  }, [content, note.content, note.id, onUpdateNote])

  // 自动保存标题
  useEffect(() => {
    const timer = setTimeout(() => {
      if (title !== note.title && title.trim()) {
        onUpdateNote(note.id, { title: title.trim() })
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [title, note.title, note.id, onUpdateNote])

  const handleTitleEdit = () => {
    setIsEditingTitle(true)
    setTimeout(() => {
      titleInputRef.current?.focus()
      titleInputRef.current?.select()
    }, 0)
  }

  const handleTitleSave = () => {
    setIsEditingTitle(false)
    if (title.trim()) {
      onUpdateNote(note.id, { title: title.trim() })
    } else {
      setTitle(note.title) // 恢复原标题
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl+S 手动保存
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault()
      onUpdateNote(note.id, { content, title: title.trim() })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className={`flex-1 flex flex-col ${isFullscreen ? 'fixed inset-0 z-40 bg-white' : 'bg-gradient-to-br from-white to-gray-50/30'}`}>
      {/* 头部 */}
      <div className="border-b border-gray-200/50 px-6 py-3 bg-white/80 backdrop-blur-sm">
        <div className="flex items-center justify-between mb-2">
          {isEditingTitle ? (
            <input
              ref={titleInputRef}
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              onBlur={handleTitleSave}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleTitleSave()
                } else if (e.key === 'Escape') {
                  setTitle(note.title)
                  setIsEditingTitle(false)
                }
              }}
              className="text-lg font-semibold text-gray-800 bg-transparent border-none outline-none focus:ring-0 w-full"
            />
          ) : (
            <h1
              className="text-lg font-semibold text-gray-800 cursor-pointer hover:text-blue-600 transition-colors duration-200 flex items-center space-x-2"
              onClick={handleTitleEdit}
              title="点击编辑标题"
            >
              <span>{note.title}</span>
              <svg className="w-4 h-4 opacity-0 hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </h1>
          )}

          <div className="flex items-center space-x-4">
            {/* 工具栏按钮 */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowPreview(!showPreview)}
                className={`p-2 rounded-lg transition-colors ${showPreview ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100'}`}
                title="预览模式"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>

              <button
                onClick={onToggleFullscreen}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                title={isFullscreen ? "退出全屏" : "全屏模式"}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {isFullscreen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0-4.5l5.5 5.5" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  )}
                </svg>
              </button>
            </div>

            <div className="flex items-center space-x-3 text-xs text-gray-500 bg-gray-50 px-3 py-1.5 rounded-full">
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                <span>{wordCount} 词</span>
              </div>
              <div className="w-px h-3 bg-gray-300"></div>
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>{charCount} 字符</span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-xs text-gray-400 flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>创建于 {formatDate(note.createdAt)}</span>
          </div>
          {note.updatedAt !== note.createdAt && (
            <>
              <div className="w-px h-3 bg-gray-300"></div>
              <div className="flex items-center space-x-1">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>更新于 {formatDate(note.updatedAt)}</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* 编辑器 */}
      <div className="flex-1 p-6">
        <div className="h-full rounded-xl border border-gray-200/50 shadow-sm overflow-hidden flex">
          {showPreview ? (
            /* 预览模式 - 纯语法高亮显示 */
            <div className="w-full h-full overflow-auto p-6" style={{ backgroundColor: theme.colors.background }}>
              <SyntaxHighlighter
                content={content}
                theme={theme}
                fontSize={settings.fontSize}
                showLineNumbers={settings.showLineNumbers}
                className="h-full"
              />
            </div>
          ) : (
            /* 编辑模式 - 分屏显示 */
            <>
              {/* 左侧：普通文本编辑器 */}
              <div className="w-1/2 h-full border-r border-gray-200">
                <div className="h-full flex flex-col">
                  <div className="px-3 py-2 bg-gray-50 border-b border-gray-200 text-xs text-gray-600 font-medium">
                    编辑模式
                  </div>
                  <textarea
                    ref={textareaRef}
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="开始写作..."
                    className="flex-1 w-full resize-none border-none outline-none bg-white leading-relaxed editor-content p-4"
                    style={{
                      fontFamily: '"Segoe UI", "Microsoft YaHei", Tahoma, Geneva, Verdana, sans-serif',
                      fontSize: `${settings.fontSize}px`,
                      lineHeight: `${settings.fontSize * 1.7}px`,
                      color: theme.colors.text
                    }}
                    spellCheck={false}
                  />
                </div>
              </div>

              {/* 右侧：语法高亮预览 */}
              <div className="w-1/2 h-full">
                <div className="h-full flex flex-col">
                  <div className="px-3 py-2 bg-gray-50 border-b border-gray-200 text-xs text-gray-600 font-medium">
                    语法高亮预览
                  </div>
                  <div className="flex-1 overflow-auto p-4" style={{ backgroundColor: theme.colors.background }}>
                    <SyntaxHighlighter
                      content={content}
                      theme={theme}
                      fontSize={settings.fontSize}
                      showLineNumbers={settings.showLineNumbers}
                      className="h-full"
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* 底部状态栏 */}
      <div className="border-t border-gray-200/50 px-6 py-2 bg-white/80 backdrop-blur-sm">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-1">
              <svg className="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>自动保存已开启</span>
            </div>
            <div className="flex items-center space-x-1">
              <kbd className="px-1.5 py-0.5 text-xs font-mono bg-gray-100 rounded border">Ctrl</kbd>
              <span>+</span>
              <kbd className="px-1.5 py-0.5 text-xs font-mono bg-gray-100 rounded border">S</kbd>
              <span>手动保存</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {content !== note.content ? (
              <div className="flex items-center space-x-1 text-orange-500">
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                <span>未保存</span>
              </div>
            ) : (
              <div className="flex items-center space-x-1 text-green-500">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>已保存</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Editor
