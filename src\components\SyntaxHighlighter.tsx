import React, { useMemo } from 'react'
import { ColorTheme } from '../types'

interface SyntaxHighlighterProps {
  content: string
  theme: ColorTheme
  fontSize: number
  showLineNumbers: boolean
  className?: string
}

interface TextSegment {
  text: string
  type: 'chinese' | 'english' | 'number' | 'punctuation' | 'whitespace'
}

const SyntaxHighlighter: React.FC<SyntaxHighlighterProps> = ({
  content,
  theme,
  fontSize,
  showLineNumbers,
  className = ''
}) => {
  // 解析文本并分类字符
  const parseText = (text: string): TextSegment[] => {
    const segments: TextSegment[] = []
    let currentSegment = ''
    let currentType: TextSegment['type'] | null = null

    const getCharType = (char: string): TextSegment['type'] => {
      // 数字 - 优先检查，避免被其他规则覆盖
      if (/[0-9]/.test(char)) {
        return 'number'
      }
      // 英文字母 - 只匹配纯英文字母
      if (/[a-zA-Z]/.test(char)) {
        return 'english'
      }
      // 汉字范围 - 常用汉字区间
      if (/[\u4e00-\u9fff]/.test(char)) {
        return 'chinese'
      }
      // 空白字符
      if (/\s/.test(char)) {
        return 'whitespace'
      }
      // 其他标点符号和特殊字符
      return 'punctuation'
    }

    for (let i = 0; i < text.length; i++) {
      const char = text[i]
      const charType = getCharType(char)

      if (currentType === charType) {
        currentSegment += char
      } else {
        if (currentSegment && currentType) {
          segments.push({ text: currentSegment, type: currentType })
        }
        currentSegment = char
        currentType = charType
      }
    }

    if (currentSegment && currentType) {
      segments.push({ text: currentSegment, type: currentType })
    }

    return segments
  }

  // 渲染带高亮的文本
  const highlightedContent = useMemo(() => {
    const lines = content.split('\n')
    
    return lines.map((line, lineIndex) => {
      const segments = parseText(line)
      
      return (
        <div key={lineIndex} className="flex">
          {showLineNumbers && (
            <div 
              className="select-none text-right pr-4 border-r border-gray-200 mr-4 flex-shrink-0"
              style={{ 
                color: theme.colors.punctuation,
                fontSize: `${fontSize - 2}px`,
                lineHeight: `${fontSize * 1.7}px`,
                width: '40px'
              }}
            >
              {lineIndex + 1}
            </div>
          )}
          <div className="flex-1" style={{ lineHeight: `${fontSize * 1.7}px` }}>
            {segments.map((segment, segmentIndex) => {
              let color = theme.colors.text
              let backgroundColor = 'transparent'

              switch (segment.type) {
                case 'chinese':
                  color = theme.colors.chinese
                  break
                case 'english':
                  color = theme.colors.english
                  break
                case 'number':
                  color = theme.colors.number
                  break
                case 'punctuation':
                  color = theme.colors.punctuation
                  break
                case 'whitespace':
                  color = theme.colors.text
                  break
              }

              return (
                <span
                  key={segmentIndex}
                  style={{
                    color,
                    backgroundColor,
                    fontSize: `${fontSize}px`,
                    fontFamily: '"Segoe UI", "Microsoft YaHei", Tahoma, Geneva, Verdana, sans-serif',
                    fontWeight: segment.type === 'number' ? 'bold' : 'normal'
                  }}
                  title={`类型: ${segment.type}`}
                >
                  {segment.text}
                </span>
              )
            })}
            {line === '' && <br />}
          </div>
        </div>
      )
    })
  }, [content, theme, fontSize, showLineNumbers])

  return (
    <div 
      className={`font-mono leading-relaxed ${className}`}
      style={{ 
        backgroundColor: theme.colors.background,
        color: theme.colors.text,
        fontSize: `${fontSize}px`
      }}
    >
      {highlightedContent}
    </div>
  )
}

export default SyntaxHighlighter
