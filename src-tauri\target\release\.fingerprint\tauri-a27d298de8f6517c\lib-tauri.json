{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 2525464132810121730, "deps": [[40386456601120721, "percent_encoding", false, 1515400898122343614], [442785307232013896, "tauri_runtime", false, 2671217667087940474], [1200537532907108615, "url<PERSON><PERSON>n", false, 897612557370458945], [3150220818285335163, "url", false, 3275138879830704218], [4143744114649553716, "raw_window_handle", false, 14479022930878574167], [4341921533227644514, "muda", false, 9088150832458001007], [4919829919303820331, "serialize_to_javascript", false, 12895280176434263877], [5986029879202738730, "log", false, 843734491964513656], [7752760652095876438, "tauri_runtime_wry", false, 11466654430773591065], [8539587424388551196, "webview2_com", false, 5257617334926221553], [9010263965687315507, "http", false, 5964894303058347841], [9228235415475680086, "tauri_macros", false, 5024234695773331145], [9538054652646069845, "tokio", false, 4268101175217284067], [9689903380558560274, "serde", false, 18048549676231346274], [9920160576179037441, "getrandom", false, 16419047294447529622], [10229185211513642314, "mime", false, 13232945190744356214], [10629569228670356391, "futures_util", false, 15996513255906915379], [10755362358622467486, "build_script_build", false, 1650086615291311742], [10806645703491011684, "thiserror", false, 11436562525379408735], [11050281405049894993, "tauri_utils", false, 17351721423699591450], [11989259058781683633, "dunce", false, 10641527445418127707], [12565293087094287914, "window_vibrancy", false, 6173588597605050129], [12986574360607194341, "serde_repr", false, 16952185800994395701], [13077543566650298139, "heck", false, 9061310286556050106], [13116089016666501665, "windows", false, 6604976410989765120], [13625485746686963219, "anyhow", false, 16585617708773024019], [15367738274754116744, "serde_json", false, 13048289712609217873], [16928111194414003569, "dirs", false, 17628846889289625433], [17155886227862585100, "glob", false, 6016970407974840514]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-a27d298de8f6517c\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}