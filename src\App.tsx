import React, { useState, useEffect } from 'react'
import { invoke } from '@tauri-apps/api/core'
import Sidebar from './components/Sidebar'
import Editor from './components/Editor'
import SettingsPanel from './components/SettingsPanel'
import { Note, AppSettings, ColorTheme, DEFAULT_THEMES } from './types'

function App() {
  const [notes, setNotes] = useState<Note[]>([])
  const [activeNoteId, setActiveNoteId] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [settings, setSettings] = useState<AppSettings>({
    currentTheme: 'default',
    customThemes: [],
    fontSize: 16,
    showLineNumbers: false,
    isDarkMode: false,
    isFullscreen: false
  })

  // 获取当前活动的笔记
  const activeNote = notes.find(note => note.id === activeNoteId)

  // 获取当前主题
  const allThemes = [...DEFAULT_THEMES, ...settings.customThemes]
  const currentTheme = allThemes.find(theme => theme.id === settings.currentTheme) || DEFAULT_THEMES[0]

  // 加载笔记数据和设置
  useEffect(() => {
    loadNotes()
    loadSettings()
  }, [])

  // 保存设置
  useEffect(() => {
    saveSettings()
  }, [settings])

  const loadNotes = async () => {
    try {
      const savedNotes = await invoke<string>('load_notes')
      const parsedNotes = JSON.parse(savedNotes) as Note[]
      setNotes(parsedNotes)
      
      // 如果有笔记且没有选中的笔记，选中第一个
      if (parsedNotes.length > 0 && !activeNoteId) {
        setActiveNoteId(parsedNotes[0].id)
      }
    } catch (error) {
      console.error('加载笔记失败:', error)
      // 如果加载失败，创建一个默认笔记
      createNewNote()
    }
  }

  const saveNotes = async (updatedNotes: Note[]) => {
    try {
      await invoke('save_notes', { notes: JSON.stringify(updatedNotes) })
    } catch (error) {
      console.error('保存笔记失败:', error)
    }
  }

  const loadSettings = async () => {
    try {
      const savedSettings = await invoke<string>('load_settings')
      const parsedSettings = JSON.parse(savedSettings) as AppSettings
      setSettings(parsedSettings)
    } catch (error) {
      console.error('加载设置失败:', error)
      // 使用默认设置
    }
  }

  const saveSettings = async () => {
    try {
      await invoke('save_settings', { settings: JSON.stringify(settings) })
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  }

  const createNewNote = () => {
    const newNote: Note = {
      id: Date.now().toString(),
      title: '新建笔记',
      content: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    const updatedNotes = [newNote, ...notes]
    setNotes(updatedNotes)
    setActiveNoteId(newNote.id)
    saveNotes(updatedNotes)
  }

  const updateNote = (id: string, updates: Partial<Note>) => {
    const updatedNotes = notes.map(note => 
      note.id === id 
        ? { ...note, ...updates, updatedAt: new Date().toISOString() }
        : note
    )
    setNotes(updatedNotes)
    saveNotes(updatedNotes)
  }

  const deleteNote = (id: string) => {
    const updatedNotes = notes.filter(note => note.id !== id)
    setNotes(updatedNotes)
    
    // 如果删除的是当前活动笔记，选择下一个笔记
    if (activeNoteId === id) {
      const remainingNotes = updatedNotes
      setActiveNoteId(remainingNotes.length > 0 ? remainingNotes[0].id : null)
    }
    
    saveNotes(updatedNotes)
  }

  // 过滤笔记
  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    note.content.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="flex h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* 左侧边栏 */}
      {!isFullscreen && (
        <Sidebar
          notes={filteredNotes}
          activeNoteId={activeNoteId}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onNoteSelect={setActiveNoteId}
          onNewNote={createNewNote}
          onDeleteNote={deleteNote}
          onUpdateNote={updateNote}
          onShowSettings={() => setShowSettings(true)}
        />
      )}

      {/* 右侧编辑器 */}
      <div className="flex-1 flex flex-col">
        {activeNote ? (
          <Editor
            note={activeNote}
            onUpdateNote={updateNote}
            theme={currentTheme}
            settings={settings}
            isFullscreen={isFullscreen}
            onToggleFullscreen={() => setIsFullscreen(!isFullscreen)}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl flex items-center justify-center">
                <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-800 mb-3">欢迎使用记事本</h2>
              <p className="text-gray-500 mb-6 leading-relaxed">
                选择左侧的笔记开始编辑，或点击 + 号创建新笔记。
                <br />
                现在支持语法高亮和自定义主题！
              </p>
              <div className="flex flex-col space-y-3">
                <button
                  onClick={createNewNote}
                  className="inline-flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span>创建第一个笔记</span>
                </button>
                <button
                  onClick={() => setShowSettings(true)}
                  className="inline-flex items-center justify-center space-x-2 px-6 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-xl transition-all duration-200"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span>自定义主题</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 设置面板 */}
      <SettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        settings={settings}
        onSettingsChange={setSettings}
        currentTheme={currentTheme}
      />
    </div>
  )
}

export default App
