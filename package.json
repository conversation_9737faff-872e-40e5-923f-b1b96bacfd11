{"name": "notepad-app", "version": "1.0.0", "description": "现代化记事本应用", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@tauri-apps/api": "^2.0.0", "@types/react-syntax-highlighter": "^15.5.13", "monaco-editor": "^0.52.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^4.4.0"}}