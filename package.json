{"name": "notepad-app", "version": "1.0.0", "description": "现代化记事本应用", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.37.1", "@monaco-editor/react": "^4.7.0", "@tauri-apps/api": "^2.0.0", "@uiw/react-codemirror": "^4.23.12", "monaco-editor": "^0.52.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^4.4.0"}}