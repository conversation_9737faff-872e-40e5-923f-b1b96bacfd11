{"rustc": 10895048813736897673, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 11661522154930815890, "deps": [[9620753569207166497, "zerovec_derive", false, 13638407598101235400], [10706449961930108323, "yoke", false, 12482246076802657874], [17046516144589451410, "zerofrom", false, 12642305309834509764]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-7378e39daf082e97\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}