use std::fs;
use std::path::PathBuf;
use tauri::Manager;

// 获取数据目录路径
fn get_data_dir(app_handle: &tauri::AppHandle) -> Result<PathBuf, String> {
    app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("获取应用数据目录失败: {}", e))
}

// 获取笔记文件路径
fn get_notes_file_path(app_handle: &tauri::AppHandle) -> Result<PathBuf, String> {
    let mut data_dir = get_data_dir(app_handle)?;

    // 确保数据目录存在
    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("创建数据目录失败: {}", e))?;
    }

    data_dir.push("notes.json");
    Ok(data_dir)
}

// 加载笔记
#[tauri::command]
async fn load_notes(app_handle: tauri::AppHandle) -> Result<String, String> {
    let notes_file = get_notes_file_path(&app_handle)?;

    if notes_file.exists() {
        fs::read_to_string(notes_file)
            .map_err(|e| format!("读取笔记文件失败: {}", e))
    } else {
        // 如果文件不存在，返回空数组
        Ok("[]".to_string())
    }
}

// 保存笔记
#[tauri::command]
async fn save_notes(app_handle: tauri::AppHandle, notes: String) -> Result<(), String> {
    let notes_file = get_notes_file_path(&app_handle)?;

    fs::write(notes_file, notes)
        .map_err(|e| format!("保存笔记文件失败: {}", e))
}

// 获取设置文件路径
fn get_settings_file_path(app_handle: &tauri::AppHandle) -> Result<PathBuf, String> {
    let mut data_dir = get_data_dir(app_handle)?;

    // 确保数据目录存在
    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("创建数据目录失败: {}", e))?;
    }

    data_dir.push("settings.json");
    Ok(data_dir)
}

// 加载设置
#[tauri::command]
async fn load_settings(app_handle: tauri::AppHandle) -> Result<String, String> {
    let settings_file = get_settings_file_path(&app_handle)?;

    if settings_file.exists() {
        fs::read_to_string(settings_file)
            .map_err(|e| format!("读取设置文件失败: {}", e))
    } else {
        // 如果文件不存在，返回默认设置
        Ok(r#"{"currentTheme":"default","customThemes":[],"fontSize":16,"showLineNumbers":false,"isDarkMode":false,"isFullscreen":false}"#.to_string())
    }
}

// 保存设置
#[tauri::command]
async fn save_settings(app_handle: tauri::AppHandle, settings: String) -> Result<(), String> {
    let settings_file = get_settings_file_path(&app_handle)?;

    fs::write(settings_file, settings)
        .map_err(|e| format!("保存设置文件失败: {}", e))
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .invoke_handler(tauri::generate_handler![load_notes, save_notes, load_settings, save_settings])
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
