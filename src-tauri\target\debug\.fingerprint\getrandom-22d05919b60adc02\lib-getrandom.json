{"rustc": 10895048813736897673, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 2225463790103693989, "path": 12566352552838965609, "deps": [[5170503507811329045, "build_script_build", false, 4302061788654014354], [10411997081178400487, "cfg_if", false, 9886716789947666174]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-22d05919b60adc02\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}