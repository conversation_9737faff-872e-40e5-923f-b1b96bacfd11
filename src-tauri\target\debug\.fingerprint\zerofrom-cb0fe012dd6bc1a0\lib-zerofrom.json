{"rustc": 10895048813736897673, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 15657897354478470176, "path": 7595011849495289962, "deps": [[4022439902832367970, "zerofrom_derive", false, 12516078435983519260]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-cb0fe012dd6bc1a0\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}