export interface Note {
  id: string
  title: string
  content: string
  createdAt: string
  updatedAt: string
}

export interface ColorTheme {
  id: string
  name: string
  colors: {
    chinese: string      // 汉字颜色
    english: string      // 英文颜色
    number: string       // 数字颜色
    punctuation: string  // 标点符号颜色
    background: string   // 编辑器背景色
    text: string         // 默认文字颜色
  }
}

export interface AppSettings {
  currentTheme: string
  customThemes: ColorTheme[]
  fontSize: number
  showLineNumbers: boolean
  isDarkMode: boolean
  isFullscreen: boolean
}

export const DEFAULT_THEMES: ColorTheme[] = [
  {
    id: 'default',
    name: '默认主题',
    colors: {
      chinese: '#1e40af',      // 深蓝色 - 汉字
      english: '#16a34a',      // 深绿色 - 英文
      number: '#dc2626',       // 红色 - 数字
      punctuation: '#6b7280',  // 灰色 - 标点
      background: '#ffffff',   // 白色背景
      text: '#374151'          // 深灰文字
    }
  },
  {
    id: 'forest',
    name: '护眼森林',
    colors: {
      chinese: '#065f46',      // 深绿
      english: '#0891b2',      // 青色
      number: '#b45309',       // 橙色
      punctuation: '#6b7280',  // 灰色
      background: '#f0fdf4',   // 浅绿背景
      text: '#1f2937'          // 深灰文字
    }
  },
  {
    id: 'sunset',
    name: '暖色夕阳',
    colors: {
      chinese: '#dc2626',      // 红色
      english: '#ea580c',      // 橙色
      number: '#ca8a04',       // 黄色
      punctuation: '#78716c',  // 棕灰
      background: '#fefce8',   // 浅黄背景
      text: '#292524'          // 深棕文字
    }
  },
  {
    id: 'ocean',
    name: '深海蓝调',
    colors: {
      chinese: '#1e40af',      // 深蓝
      english: '#0891b2',      // 青蓝
      number: '#7c3aed',       // 紫色
      punctuation: '#64748b',  // 蓝灰
      background: '#f8fafc',   // 浅蓝背景
      text: '#1e293b'          // 深蓝灰文字
    }
  },
  {
    id: 'dark',
    name: '夜间模式',
    colors: {
      chinese: '#60a5fa',      // 亮蓝
      english: '#34d399',      // 亮绿
      number: '#f87171',       // 亮红
      punctuation: '#9ca3af',  // 亮灰
      background: '#1f2937',   // 深灰背景
      text: '#f3f4f6'          // 浅灰文字
    }
  }
]
