!function(e,t){"object"===typeof exports&&"object"===typeof module?module.exports=t(require("react"),require("react/jsx-runtime"),require("@codemirror/state"),require("@codemirror/theme-one-dark"),require("@codemirror/view")):"function"===typeof define&&define.amd?define(["react","react/jsx-runtime",,,],t):"object"===typeof exports?exports["@uiw/codemirror"]=t(require("react"),require("react/jsx-runtime"),require("@codemirror/state"),require("@codemirror/theme-one-dark"),require("@codemirror/view")):e["@uiw/codemirror"]=t(e.React,e.ReactJSXRuntime,e.CM["@codemirror/state"],e.CM["@codemirror/theme-one-dark"],e.CM["@codemirror/view"])}(self,((e,t,n,i,r)=>(()=>{"use strict";var o={60:e=>{e.exports=n},89:(e,t,n)=>{n.r(t),n.d(t,{defaultLightThemeOption:()=>a.c,getDefaultExtensions:()=>h});var i=n(720),r=n(687),o=n(730),s=n(708),l=n(60),a=n(806),c={};for(const u in s)["default","getDefaultExtensions"].indexOf(u)<0&&(c[u]=()=>s[u]);n.d(t,c);var h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.indentWithTab,n=void 0===t||t,c=e.editable,h=void 0===c||c,u=e.readOnly,f=void 0!==u&&u,d=e.theme,p=void 0===d?"light":d,m=e.placeholder,g=void 0===m?"":m,y=e.basicSetup,v=void 0===y||y,b=[];switch(n&&b.unshift(o.keymap.of([i.Yc])),v&&("boolean"===typeof v?b.unshift((0,r.o)()):b.unshift((0,r.o)(v))),g&&b.unshift((0,o.placeholder)(g)),p){case"light":b.push(a.c);break;case"dark":b.push(s.oneDark);break;case"none":break;default:b.push(p)}return!1===h&&b.push(o.EditorView.editable.of(!1)),f&&b.push(l.EditorState.readOnly.of(!0)),[].concat(b)}},194:(e,t,n)=>{n.d(t,{KB:()=>re,SG:()=>et,Zt:()=>He,Lv:()=>Le,f7:()=>Oe,tp:()=>te,_v:()=>ie,WD:()=>de,EI:()=>ne,Xt:()=>ee,jU:()=>rt,y9:()=>_e,mv:()=>z});var i=n(203),r=n(60),o=n(730);let s=0;class l{constructor(e,t,n,i){this.name=e,this.set=t,this.base=n,this.modified=i,this.id=s++}toString(){let{name:e}=this;for(let t of this.modified)t.name&&(e=`${t.name}(${e})`);return e}static define(e,t){let n="string"==typeof e?e:"?";if(e instanceof l&&(t=e),null===t||void 0===t?void 0:t.base)throw new Error("Can not derive from a modified tag");let i=new l(n,[],null,[]);if(i.set.push(i),t)for(let r of t.set)i.set.push(r);return i}static defineModifier(e){let t=new c(e);return e=>e.modified.indexOf(t)>-1?e:c.get(e.base||e,e.modified.concat(t).sort(((e,t)=>e.id-t.id)))}}let a=0;class c{constructor(e){this.name=e,this.instances=[],this.id=a++}static get(e,t){if(!t.length)return e;let n=t[0].instances.find((n=>{return n.base==e&&(i=t,r=n.modified,i.length==r.length&&i.every(((e,t)=>e==r[t])));var i,r}));if(n)return n;let i=[],r=new l(e.name,i,e,t);for(let s of t)s.instances.push(r);let o=function(e){let t=[[]];for(let n=0;n<e.length;n++)for(let i=0,r=t.length;i<r;i++)t.push(t[i].concat(e[n]));return t.sort(((e,t)=>t.length-e.length))}(t);for(let s of e.set)if(!s.modified.length)for(let e of o)i.push(c.get(s,e));return r}}function h(e){let t=Object.create(null);for(let n in e){let i=e[n];Array.isArray(i)||(i=[i]);for(let e of n.split(" "))if(e){let n=[],r=2,o=e;for(let t=0;;){if("..."==o&&t>0&&t+3==e.length){r=1;break}let i=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(o);if(!i)throw new RangeError("Invalid path: "+e);if(n.push("*"==i[0]?"":'"'==i[0][0]?JSON.parse(i[0]):i[0]),t+=i[0].length,t==e.length)break;let s=e[t++];if(t==e.length&&"!"==s){r=0;break}if("/"!=s)throw new RangeError("Invalid path: "+e);o=e.slice(t)}let s=n.length-1,l=n[s];if(!l)throw new RangeError("Invalid path: "+e);let a=new f(i,r,s>0?n.slice(0,s):null);t[l]=a.sort(t[l])}}return u.add(t)}const u=new i.uY;class f{constructor(e,t,n,i){this.tags=e,this.mode=t,this.context=n,this.next=i}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}function d(e,t){let n=Object.create(null);for(let o of e)if(Array.isArray(o.tag))for(let e of o.tag)n[e.id]=o.class;else n[o.tag.id]=o.class;let{scope:i,all:r=null}=t||{};return{style:e=>{let t=r;for(let i of e)for(let e of i.set){let i=n[e.id];if(i){t=t?t+" "+i:i;break}}return t},scope:i}}function p(e,t,n,i=0,r=e.length){let o=new m(i,Array.isArray(t)?t:[t],n);o.highlightRange(e.cursor(),i,r,"",o.highlighters),o.flush(r)}f.empty=new f([],2,null);class m{constructor(e,t,n){this.at=e,this.highlighters=t,this.span=n,this.class=""}startSpan(e,t){t!=this.class&&(this.flush(e),e>this.at&&(this.at=e),this.class=t)}flush(e){e>this.at&&this.class&&this.span(this.at,e,this.class)}highlightRange(e,t,n,r,o){let{type:s,from:l,to:a}=e;if(l>=n||a<=t)return;s.isTop&&(o=this.highlighters.filter((e=>!e.scope||e.scope(s))));let c=r,h=function(e){let t=e.type.prop(u);for(;t&&t.context&&!e.matchContext(t.context);)t=t.next;return t||null}(e)||f.empty,d=function(e,t){let n=null;for(let i of e){let e=i.style(t);e&&(n=n?n+" "+e:e)}return n}(o,h.tags);if(d&&(c&&(c+=" "),c+=d,1==h.mode&&(r+=(r?" ":"")+d)),this.startSpan(Math.max(t,l),c),h.opaque)return;let p=e.tree&&e.tree.prop(i.uY.mounted);if(p&&p.overlay){let i=e.node.enter(p.overlay[0].from+l,1),s=this.highlighters.filter((e=>!e.scope||e.scope(p.tree.type))),h=e.firstChild();for(let u=0,f=l;;u++){let d=u<p.overlay.length?p.overlay[u]:null,m=d?d.from+l:a,g=Math.max(t,f),y=Math.min(n,m);if(g<y&&h)for(;e.from<y&&(this.highlightRange(e,g,y,r,o),this.startSpan(Math.min(y,e.to),c),!(e.to>=m)&&e.nextSibling()););if(!d||m>n)break;f=d.to+l,f>t&&(this.highlightRange(i.cursor(),Math.max(t,d.from+l),Math.min(n,f),"",s),this.startSpan(Math.min(n,f),c))}h&&e.parent()}else if(e.firstChild()){p&&(r="");do{if(!(e.to<=t)){if(e.from>=n)break;this.highlightRange(e,t,n,r,o),this.startSpan(Math.min(n,e.to),c)}}while(e.nextSibling());e.parent()}}}const g=l.define,y=g(),v=g(),b=g(v),w=g(v),x=g(),k=g(x),S=g(x),C=g(),A=g(C),E=g(),O=g(),D=g(),M=g(D),P=g(),T={comment:y,lineComment:g(y),blockComment:g(y),docComment:g(y),name:v,variableName:g(v),typeName:b,tagName:g(b),propertyName:w,attributeName:g(w),className:g(v),labelName:g(v),namespace:g(v),macroName:g(v),literal:x,string:k,docString:g(k),character:g(k),attributeValue:g(k),number:S,integer:g(S),float:g(S),bool:g(x),regexp:g(x),escape:g(x),color:g(x),url:g(x),keyword:E,self:g(E),null:g(E),atom:g(E),unit:g(E),modifier:g(E),operatorKeyword:g(E),controlKeyword:g(E),definitionKeyword:g(E),moduleKeyword:g(E),operator:O,derefOperator:g(O),arithmeticOperator:g(O),logicOperator:g(O),bitwiseOperator:g(O),compareOperator:g(O),updateOperator:g(O),definitionOperator:g(O),typeOperator:g(O),controlOperator:g(O),punctuation:D,separator:g(D),bracket:M,angleBracket:g(M),squareBracket:g(M),paren:g(M),brace:g(M),content:C,heading:A,heading1:g(A),heading2:g(A),heading3:g(A),heading4:g(A),heading5:g(A),heading6:g(A),contentSeparator:g(C),list:g(C),quote:g(C),emphasis:g(C),strong:g(C),link:g(C),monospace:g(C),strikethrough:g(C),inserted:g(),deleted:g(),changed:g(),invalid:g(),meta:P,documentMeta:g(P),annotation:g(P),processingInstruction:g(P),definition:l.defineModifier("definition"),constant:l.defineModifier("constant"),function:l.defineModifier("function"),standard:l.defineModifier("standard"),local:l.defineModifier("local"),special:l.defineModifier("special")};for(let dt in T){let e=T[dt];e instanceof l&&(e.name=dt)}d([{tag:T.link,class:"tok-link"},{tag:T.heading,class:"tok-heading"},{tag:T.emphasis,class:"tok-emphasis"},{tag:T.strong,class:"tok-strong"},{tag:T.keyword,class:"tok-keyword"},{tag:T.atom,class:"tok-atom"},{tag:T.bool,class:"tok-bool"},{tag:T.url,class:"tok-url"},{tag:T.labelName,class:"tok-labelName"},{tag:T.inserted,class:"tok-inserted"},{tag:T.deleted,class:"tok-deleted"},{tag:T.literal,class:"tok-literal"},{tag:T.string,class:"tok-string"},{tag:T.number,class:"tok-number"},{tag:[T.regexp,T.escape,T.special(T.string)],class:"tok-string2"},{tag:T.variableName,class:"tok-variableName"},{tag:T.local(T.variableName),class:"tok-variableName tok-local"},{tag:T.definition(T.variableName),class:"tok-variableName tok-definition"},{tag:T.special(T.variableName),class:"tok-variableName2"},{tag:T.definition(T.propertyName),class:"tok-propertyName tok-definition"},{tag:T.typeName,class:"tok-typeName"},{tag:T.namespace,class:"tok-namespace"},{tag:T.className,class:"tok-className"},{tag:T.macroName,class:"tok-macroName"},{tag:T.propertyName,class:"tok-propertyName"},{tag:T.operator,class:"tok-operator"},{tag:T.comment,class:"tok-comment"},{tag:T.meta,class:"tok-meta"},{tag:T.invalid,class:"tok-invalid"},{tag:T.punctuation,class:"tok-punctuation"}]);const I="undefined"==typeof Symbol?"__\u037c":Symbol.for("\u037c"),B="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class R{constructor(e,t){this.rules=[];let{finish:n}=t||{};function i(e){return/^@/.test(e)?[e]:e.split(/,\s*/)}function r(e,t,o,s){let l=[],a=/^@(\w+)\b/.exec(e[0]),c=a&&"keyframes"==a[1];if(a&&null==t)return o.push(e[0]+";");for(let n in t){let s=t[n];if(/&/.test(n))r(n.split(/,\s*/).map((t=>e.map((e=>t.replace(/&/,e))))).reduce(((e,t)=>e.concat(t))),s,o);else if(s&&"object"==typeof s){if(!a)throw new RangeError("The value of a property ("+n+") should be a primitive value.");r(i(n),s,l,c)}else null!=s&&l.push(n.replace(/_.*/,"").replace(/[A-Z]/g,(e=>"-"+e.toLowerCase()))+": "+s+";")}(l.length||c)&&o.push((!n||a||s?e:e.map(n)).join(", ")+" {"+l.join(" ")+"}")}for(let o in e)r(i(o),e[o],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let e=N[I]||1;return N[I]=e+1,"\u037c"+e.toString(36)}static mount(e,t,n){let i=e[B],r=n&&n.nonce;i?r&&i.setNonce(r):i=new j(e,r),i.mount(Array.isArray(t)?t:[t],e)}}let L=new Map;class j{constructor(e,t){let n=e.ownerDocument||e,i=n.defaultView;if(!e.head&&e.adoptedStyleSheets&&i.CSSStyleSheet){let t=L.get(n);if(t)return e[B]=t;this.sheet=new i.CSSStyleSheet,L.set(n,this)}else this.styleTag=n.createElement("style"),t&&this.styleTag.setAttribute("nonce",t);this.modules=[],e[B]=this}mount(e,t){let n=this.sheet,i=0,r=0;for(let o=0;o<e.length;o++){let t=e[o],s=this.modules.indexOf(t);if(s<r&&s>-1&&(this.modules.splice(s,1),r--,s=-1),-1==s){if(this.modules.splice(r++,0,t),n)for(let e=0;e<t.rules.length;e++)n.insertRule(t.rules[e],i++)}else{for(;r<s;)i+=this.modules[r++].rules.length;i+=t.rules.length,r++}}if(n)t.adoptedStyleSheets.indexOf(this.sheet)<0&&(t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets]);else{let e="";for(let t=0;t<this.modules.length;t++)e+=this.modules[t].getRules()+"\n";this.styleTag.textContent=e;let n=t.head||t;this.styleTag.parentNode!=n&&n.insertBefore(this.styleTag,n.firstChild)}}setNonce(e){this.styleTag&&this.styleTag.getAttribute("nonce")!=e&&this.styleTag.setAttribute("nonce",e)}}var F;const W=new i.uY;const V=new i.uY;class q{constructor(e,t,n=[],i=""){this.data=e,this.name=i,r.EditorState.prototype.hasOwnProperty("tree")||Object.defineProperty(r.EditorState.prototype,"tree",{get(){return z(this)}}),this.parser=t,this.extension=[Q.of(this),r.EditorState.languageData.of(((e,t,n)=>{let i=_(e,t,n),r=i.type.prop(W);if(!r)return[];let o=e.facet(r),s=i.type.prop(V);if(s){let r=i.resolve(t-i.from,n);for(let t of s)if(t.test(r,e)){let n=e.facet(t.facet);return"replace"==t.type?n:n.concat(o)}}return o}))].concat(n)}isActiveAt(e,t,n=-1){return _(e,t,n).type.prop(W)==this.data}findRegions(e){let t=e.facet(Q);if((null===t||void 0===t?void 0:t.data)==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let n=[],r=(e,t)=>{if(e.prop(W)==this.data)return void n.push({from:t,to:t+e.length});let o=e.prop(i.uY.mounted);if(o){if(o.tree.prop(W)==this.data){if(o.overlay)for(let e of o.overlay)n.push({from:e.from+t,to:e.to+t});else n.push({from:t,to:t+e.length});return}if(o.overlay){let e=n.length;if(r(o.tree,o.overlay[0].from+t),n.length>e)return}}for(let n=0;n<e.children.length;n++){let o=e.children[n];o instanceof i.PH&&r(o,e.positions[n]+t)}};return r(z(e),0),n}get allowsNesting(){return!0}}function _(e,t,n){let r=e.facet(Q),o=z(e).topNode;if(!r||r.allowsNesting)for(let s=o;s;s=s.enter(t,n,i.Qj.ExcludeBuffers))s.type.isTop&&(o=s);return o}q.setState=r.StateEffect.define();function z(e){let t=e.field(q.state,!1);return t?t.tree:i.PH.empty}class U{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let n=this.cursorPos-this.string.length;return e<n||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-n,t-n)}}let H=null;class ${constructor(e,t,n=[],i,r,o,s,l){this.parser=e,this.state=t,this.fragments=n,this.tree=i,this.treeLen=r,this.viewport=o,this.skipped=s,this.scheduleOn=l,this.parse=null,this.tempSkipped=[]}static create(e,t,n){return new $(e,t,[],i.PH.empty,0,n,[],null)}startParse(){return this.parser.startParse(new U(this.state.doc),this.fragments)}work(e,t){return null!=t&&t>=this.state.doc.length&&(t=void 0),this.tree!=i.PH.empty&&this.isDone(null!==t&&void 0!==t?t:this.state.doc.length)?(this.takeTree(),!0):this.withContext((()=>{var n;if("number"==typeof e){let t=Date.now()+e;e=()=>Date.now()>t}for(this.parse||(this.parse=this.startParse()),null!=t&&(null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let r=this.parse.advance();if(r){if(this.fragments=this.withoutTempSkipped(i.rr.addTree(r,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(n=this.parse.stoppedAt)&&void 0!==n?n:this.state.doc.length,this.tree=r,this.parse=null,!(this.treeLen<(null!==t&&void 0!==t?t:this.state.doc.length)))return!0;this.parse=this.startParse()}if(e())return!1}}))}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext((()=>{for(;!(t=this.parse.advance()););})),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(i.rr.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=H;H=this;try{return e()}finally{H=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=J(e,t.from,t.to);return e}changes(e,t){let{fragments:n,tree:r,treeLen:o,viewport:s,skipped:l}=this;if(this.takeTree(),!e.empty){let t=[];if(e.iterChangedRanges(((e,n,i,r)=>t.push({fromA:e,toA:n,fromB:i,toB:r}))),n=i.rr.applyChanges(n,t),r=i.PH.empty,o=0,s={from:e.mapPos(s.from,-1),to:e.mapPos(s.to,1)},this.skipped.length){l=[];for(let t of this.skipped){let n=e.mapPos(t.from,1),i=e.mapPos(t.to,-1);n<i&&l.push({from:n,to:i})}}}return new $(this.parser,t,n,r,o,s,l,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let n=0;n<this.skipped.length;n++){let{from:t,to:i}=this.skipped[n];t<e.to&&i>e.from&&(this.fragments=J(this.fragments,t,i),this.skipped.splice(n--,1))}return!(this.skipped.length>=t)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends i.iX{createParse(t,n,r){let o=r[0].from,s=r[r.length-1].to;return{parsedPos:o,advance(){let t=H;if(t){for(let e of r)t.tempSkipped.push(e);e&&(t.scheduleOn=t.scheduleOn?Promise.all([t.scheduleOn,e]):e)}return this.parsedPos=s,new i.PH(i.Z6.none,[],[],s-o)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&0==t[0].from&&t[0].to>=e}static get(){return H}}function J(e,t,n){return i.rr.applyChanges(e,[{fromA:t,toA:n,fromB:t,toB:n}])}class K{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),n=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,n)||t.takeTree(),new K(t)}static init(e){let t=Math.min(3e3,e.doc.length),n=$.create(e.facet(Q).parser,e,{from:0,to:t});return n.work(20,t)||n.takeTree(),new K(n)}}q.state=r.StateField.define({create:K.init,update(e,t){for(let n of t.effects)if(n.is(q.setState))return n.value;return t.startState.facet(Q)!=t.state.facet(Q)?K.init(t.state):e.apply(t)}});let Y=e=>{let t=setTimeout((()=>e()),500);return()=>clearTimeout(t)};"undefined"!=typeof requestIdleCallback&&(Y=e=>{let t=-1,n=setTimeout((()=>{t=requestIdleCallback(e,{timeout:400})}),100);return()=>t<0?clearTimeout(n):cancelIdleCallback(t)});const G="undefined"!=typeof navigator&&(null===(F=navigator.scheduling)||void 0===F?void 0:F.isInputPending)?()=>navigator.scheduling.isInputPending():null,Z=o.ViewPlugin.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(q.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),(e.docChanged||e.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(q.state);t.tree==t.context.tree&&t.context.isDone(e.doc.length)||(this.working=Y(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:n,viewport:{to:i}}=this.view,r=n.field(q.state);if(r.tree==r.context.tree&&r.context.isDone(i+1e5))return;let o=Date.now()+Math.min(this.chunkBudget,100,e&&!G?Math.max(25,e.timeRemaining()-5):1e9),s=r.context.treeLen<i&&n.doc.length>i+1e3,l=r.context.work((()=>G&&G()||Date.now()>o),i+(s?0:1e5));this.chunkBudget-=Date.now()-t,(l||this.chunkBudget<=0)&&(r.context.takeTree(),this.view.dispatch({effects:q.setState.of(new K(r.context))})),this.chunkBudget>0&&(!l||s)&&this.scheduleWork(),this.checkAsyncSchedule(r.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then((()=>this.scheduleWork())).catch((e=>(0,o.logException)(this.view.state,e))).then((()=>this.workScheduled--)),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),Q=r.Facet.define({combine:e=>e.length?e[0]:null,enables:e=>[q.state,Z,o.EditorView.contentAttributes.compute([e],(t=>{let n=t.facet(e);return n&&n.name?{"data-language":n.name}:{}}))]});const X=r.Facet.define(),ee=r.Facet.define({combine:e=>{if(!e.length)return"  ";let t=e[0];if(!t||/\S/.test(t)||Array.from(t).some((e=>e!=t[0])))throw new Error("Invalid indent unit: "+JSON.stringify(e[0]));return t}});function te(e){let t=e.facet(ee);return 9==t.charCodeAt(0)?e.tabSize*t.length:t.length}function ne(e,t){let n="",i=e.tabSize,r=e.facet(ee)[0];if("\t"==r){for(;t>=i;)n+="\t",t-=i;r=" "}for(let o=0;o<t;o++)n+=r;return n}function ie(e,t){e instanceof r.EditorState&&(e=new re(e));for(let i of e.state.facet(X)){let n=i(e,t);if(void 0!==n)return n}let n=z(e.state);return n.length>=t?function(e,t,n){let i=t.resolveStack(n),r=t.resolveInner(n,-1).resolve(n,0).enterUnfinishedNodesBefore(n);if(r!=i.node){let e=[];for(let t=r;t&&(t.from!=i.node.from||t.type!=i.node.type);t=t.parent)e.push(t);for(let t=e.length-1;t>=0;t--)i={node:e[t],next:i}}return se(i,e,n)}(e,n,t):null}class re{constructor(e,t={}){this.state=e,this.options=t,this.unit=te(e)}lineAt(e,t=1){let n=this.state.doc.lineAt(e),{simulateBreak:i,simulateDoubleBreak:r}=this.options;return null!=i&&i>=n.from&&i<=n.to?r&&i==e?{text:"",from:e}:(t<0?i<e:i<=e)?{text:n.text.slice(i-n.from),from:i}:{text:n.text.slice(0,i-n.from),from:n.from}:n}textAfterPos(e,t=1){if(this.options.simulateDoubleBreak&&e==this.options.simulateBreak)return"";let{text:n,from:i}=this.lineAt(e,t);return n.slice(e-i,Math.min(n.length,e+100-i))}column(e,t=1){let{text:n,from:i}=this.lineAt(e,t),r=this.countColumn(n,e-i),o=this.options.overrideIndentation?this.options.overrideIndentation(i):-1;return o>-1&&(r+=o-this.countColumn(n,n.search(/\S|$/))),r}countColumn(e,t=e.length){return(0,r.countColumn)(e,this.state.tabSize,t)}lineIndent(e,t=1){let{text:n,from:i}=this.lineAt(e,t),r=this.options.overrideIndentation;if(r){let e=r(i);if(e>-1)return e}return this.countColumn(n,n.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}const oe=new i.uY;function se(e,t,n){for(let i=e;i;i=i.next){let e=le(i.node);if(e)return e(ce.create(t,n,i))}return 0}function le(e){let t=e.type.prop(oe);if(t)return t;let n,r=e.firstChild;if(r&&(n=r.type.prop(i.uY.closedBy))){let t=e.lastChild,i=t&&n.indexOf(t.name)>-1;return e=>ue(e,!0,1,void 0,i&&!function(e){return e.pos==e.options.simulateBreak&&e.options.simulateDoubleBreak}(e)?t.from:void 0)}return null==e.parent?ae:null}function ae(){return 0}class ce extends re{constructor(e,t,n){super(e.state,e.options),this.base=e,this.pos=t,this.context=n}get node(){return this.context.node}static create(e,t,n){return new ce(e,t,n)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(e){let t=this.state.doc.lineAt(e.from);for(;;){let n=e.resolve(t.from);for(;n.parent&&n.parent.from==n.from;)n=n.parent;if(he(n,e))break;t=this.state.doc.lineAt(n.from)}return this.lineIndent(t.from)}continue(){return se(this.context.next,this.base,this.pos)}}function he(e,t){for(let n=t;n;n=n.parent)if(e==n)return!0;return!1}function ue(e,t,n,i,r){let o=e.textAfter,s=o.match(/^\s*/)[0].length,l=i&&o.slice(s,s+i.length)==i||r==e.pos+s,a=t?function(e){let t=e.node,n=t.childAfter(t.from),i=t.lastChild;if(!n)return null;let r=e.options.simulateBreak,o=e.state.doc.lineAt(n.from),s=null==r||r<=o.from?o.to:Math.min(o.to,r);for(let l=n.to;;){let e=t.childAfter(l);if(!e||e==i)return null;if(!e.type.isSkipped){if(e.from>=s)return null;let t=/^ */.exec(o.text.slice(n.to-o.from))[0].length;return{from:n.from,to:n.to+t}}l=e.to}}(e):null;return a?l?e.column(a.from):e.column(a.to):e.baseIndent+(l?0:e.unit*n)}const fe=200;function de(){return r.EditorState.transactionFilter.of((e=>{if(!e.docChanged||!e.isUserEvent("input.type")&&!e.isUserEvent("input.complete"))return e;let t=e.startState.languageDataAt("indentOnInput",e.startState.selection.main.head);if(!t.length)return e;let n=e.newDoc,{head:i}=e.newSelection.main,r=n.lineAt(i);if(i>r.from+fe)return e;let o=n.sliceString(r.from,i);if(!t.some((e=>e.test(o))))return e;let{state:s}=e,l=-1,a=[];for(let{head:c}of s.selection.ranges){let e=s.doc.lineAt(c);if(e.from==l)continue;l=e.from;let t=ie(s,e.from);if(null==t)continue;let n=/^\s*/.exec(e.text)[0],i=ne(s,t);n!=i&&a.push({from:e.from,to:e.from+n.length,insert:i})}return a.length?[e,{changes:a,sequential:!0}]:e}))}const pe=r.Facet.define(),me=new i.uY;function ge(e){let t=e.lastChild;return t&&t.to==e.to&&t.type.isError}function ye(e,t,n){for(let i of e.facet(pe)){let r=i(e,t,n);if(r)return r}return function(e,t,n){let i=z(e);if(i.length<n)return null;let r=null;for(let o=i.resolveStack(n,1);o;o=o.next){let s=o.node;if(s.to<=n||s.from>n)continue;if(r&&s.from<t)break;let l=s.type.prop(me);if(l&&(s.to<i.length-50||i.length==e.doc.length||!ge(s))){let i=l(s,e);i&&i.from<=n&&i.from>=t&&i.to>n&&(r=i)}}return r}(e,t,n)}function ve(e,t){let n=t.mapPos(e.from,1),i=t.mapPos(e.to,-1);return n>=i?void 0:{from:n,to:i}}const be=r.StateEffect.define({map:ve}),we=r.StateEffect.define({map:ve});function xe(e){let t=[];for(let{head:n}of e.state.selection.ranges)t.some((e=>e.from<=n&&e.to>=n))||t.push(e.lineBlockAt(n));return t}const ke=r.StateField.define({create:()=>o.Decoration.none,update(e,t){e=e.map(t.changes);for(let n of t.effects)if(n.is(be)&&!Ce(e,n.value.from,n.value.to)){let{preparePlaceholder:i}=t.state.facet(Me),r=i?o.Decoration.replace({widget:new Be(i(t.state,n.value))}):Ie;e=e.update({add:[r.range(n.value.from,n.value.to)]})}else n.is(we)&&(e=e.update({filter:(e,t)=>n.value.from!=e||n.value.to!=t,filterFrom:n.value.from,filterTo:n.value.to}));if(t.selection){let n=!1,{head:i}=t.selection.main;e.between(i,i,((e,t)=>{e<i&&t>i&&(n=!0)})),n&&(e=e.update({filterFrom:i,filterTo:i,filter:(e,t)=>t<=i||e>=i}))}return e},provide:e=>o.EditorView.decorations.from(e),toJSON(e,t){let n=[];return e.between(0,t.doc.length,((e,t)=>{n.push(e,t)})),n},fromJSON(e){if(!Array.isArray(e)||e.length%2)throw new RangeError("Invalid JSON for fold state");let t=[];for(let n=0;n<e.length;){let i=e[n++],r=e[n++];if("number"!=typeof i||"number"!=typeof r)throw new RangeError("Invalid JSON for fold state");t.push(Ie.range(i,r))}return o.Decoration.set(t,!0)}});function Se(e,t,n){var i;let r=null;return null===(i=e.field(ke,!1))||void 0===i||i.between(t,n,((e,t)=>{(!r||r.from>e)&&(r={from:e,to:t})})),r}function Ce(e,t,n){let i=!1;return e.between(t,t,((e,r)=>{e==t&&r==n&&(i=!0)})),i}function Ae(e,t){return e.field(ke,!1)?t:t.concat(r.StateEffect.appendConfig.of(Pe()))}function Ee(e,t,n=!0){let i=e.state.doc.lineAt(t.from).number,r=e.state.doc.lineAt(t.to).number;return o.EditorView.announce.of(`${e.state.phrase(n?"Folded lines":"Unfolded lines")} ${i} ${e.state.phrase("to")} ${r}.`)}const Oe=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:e=>{for(let t of xe(e)){let n=ye(e.state,t.from,t.to);if(n)return e.dispatch({effects:Ae(e.state,[be.of(n),Ee(e,n)])}),!0}return!1}},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:e=>{if(!e.state.field(ke,!1))return!1;let t=[];for(let n of xe(e)){let i=Se(e.state,n.from,n.to);i&&t.push(we.of(i),Ee(e,i,!1))}return t.length&&e.dispatch({effects:t}),t.length>0}},{key:"Ctrl-Alt-[",run:e=>{let{state:t}=e,n=[];for(let i=0;i<t.doc.length;){let r=e.lineBlockAt(i),o=ye(t,r.from,r.to);o&&n.push(be.of(o)),i=(o?e.lineBlockAt(o.to):r).to+1}return n.length&&e.dispatch({effects:Ae(e.state,n)}),!!n.length}},{key:"Ctrl-Alt-]",run:e=>{let t=e.state.field(ke,!1);if(!t||!t.size)return!1;let n=[];return t.between(0,e.state.doc.length,((e,t)=>{n.push(we.of({from:e,to:t}))})),e.dispatch({effects:n}),!0}}],De={placeholderDOM:null,preparePlaceholder:null,placeholderText:"\u2026"},Me=r.Facet.define({combine:e=>(0,r.combineConfig)(e,De)});function Pe(e){let t=[ke,je];return e&&t.push(Me.of(e)),t}function Te(e,t){let{state:n}=e,i=n.facet(Me),r=t=>{let n=e.lineBlockAt(e.posAtDOM(t.target)),i=Se(e.state,n.from,n.to);i&&e.dispatch({effects:we.of(i)}),t.preventDefault()};if(i.placeholderDOM)return i.placeholderDOM(e,r,t);let o=document.createElement("span");return o.textContent=i.placeholderText,o.setAttribute("aria-label",n.phrase("folded code")),o.title=n.phrase("unfold"),o.className="cm-foldPlaceholder",o.onclick=r,o}const Ie=o.Decoration.replace({widget:new class extends o.WidgetType{toDOM(e){return Te(e,null)}}});class Be extends o.WidgetType{constructor(e){super(),this.value=e}eq(e){return this.value==e.value}toDOM(e){return Te(e,this.value)}}const Ne={openText:"\u2304",closedText:"\u203a",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class Re extends o.GutterMarker{constructor(e,t){super(),this.config=e,this.open=t}eq(e){return this.config==e.config&&this.open==e.open}toDOM(e){if(this.config.markerDOM)return this.config.markerDOM(this.open);let t=document.createElement("span");return t.textContent=this.open?this.config.openText:this.config.closedText,t.title=e.state.phrase(this.open?"Fold line":"Unfold line"),t}}function Le(e={}){let t=Object.assign(Object.assign({},Ne),e),n=new Re(t,!0),i=new Re(t,!1),s=o.ViewPlugin.fromClass(class{constructor(e){this.from=e.viewport.from,this.markers=this.buildMarkers(e)}update(e){(e.docChanged||e.viewportChanged||e.startState.facet(Q)!=e.state.facet(Q)||e.startState.field(ke,!1)!=e.state.field(ke,!1)||z(e.startState)!=z(e.state)||t.foldingChanged(e))&&(this.markers=this.buildMarkers(e.view))}buildMarkers(e){let t=new r.RangeSetBuilder;for(let r of e.viewportLineBlocks){let o=Se(e.state,r.from,r.to)?i:ye(e.state,r.from,r.to)?n:null;o&&t.add(r.from,r.from,o)}return t.finish()}}),{domEventHandlers:l}=t;return[s,(0,o.gutter)({class:"cm-foldGutter",markers(e){var t;return(null===(t=e.plugin(s))||void 0===t?void 0:t.markers)||r.RangeSet.empty},initialSpacer:()=>new Re(t,!1),domEventHandlers:Object.assign(Object.assign({},l),{click:(e,t,n)=>{if(l.click&&l.click(e,t,n))return!0;let i=Se(e.state,t.from,t.to);if(i)return e.dispatch({effects:we.of(i)}),!0;let r=ye(e.state,t.from,t.to);return!!r&&(e.dispatch({effects:be.of(r)}),!0)}})}),Pe()]}const je=o.EditorView.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class Fe{constructor(e,t){let n;function i(e){let t=R.newName();return(n||(n=Object.create(null)))["."+t]=e,t}this.specs=e;const r="string"==typeof t.all?t.all:t.all?i(t.all):void 0,o=t.scope;this.scope=o instanceof q?e=>e.prop(W)==o.data:o?e=>e==o:void 0,this.style=d(e.map((e=>({tag:e.tag,class:e.class||i(Object.assign({},e,{tag:null}))}))),{all:r}).style,this.module=n?new R(n):null,this.themeType=t.themeType}static define(e,t){return new Fe(e,t||{})}}const We=r.Facet.define(),Ve=r.Facet.define({combine:e=>e.length?[e[0]]:null});function qe(e){let t=e.facet(We);return t.length?t:e.facet(Ve)}function _e(e,t){let n,i=[Ue];return e instanceof Fe&&(e.module&&i.push(o.EditorView.styleModule.of(e.module)),n=e.themeType),(null===t||void 0===t?void 0:t.fallback)?i.push(Ve.of(e)):n?i.push(We.computeN([o.EditorView.darkTheme],(t=>t.facet(o.EditorView.darkTheme)==("dark"==n)?[e]:[]))):i.push(We.of(e)),i}class ze{constructor(e){this.markCache=Object.create(null),this.tree=z(e.state),this.decorations=this.buildDeco(e,qe(e.state)),this.decoratedTo=e.viewport.to}update(e){let t=z(e.state),n=qe(e.state),i=n!=qe(e.startState),{viewport:r}=e.view,o=e.changes.mapPos(this.decoratedTo,1);t.length<r.to&&!i&&t.type==this.tree.type&&o>=r.to?(this.decorations=this.decorations.map(e.changes),this.decoratedTo=o):(t!=this.tree||e.viewportChanged||i)&&(this.tree=t,this.decorations=this.buildDeco(e.view,n),this.decoratedTo=r.to)}buildDeco(e,t){if(!t||!this.tree.length)return o.Decoration.none;let n=new r.RangeSetBuilder;for(let{from:i,to:r}of e.visibleRanges)p(this.tree,t,((e,t,i)=>{n.add(e,t,this.markCache[i]||(this.markCache[i]=o.Decoration.mark({class:i})))}),i,r);return n.finish()}}const Ue=r.Prec.high(o.ViewPlugin.fromClass(ze,{decorations:e=>e.decorations})),He=Fe.define([{tag:T.meta,color:"#404740"},{tag:T.link,textDecoration:"underline"},{tag:T.heading,textDecoration:"underline",fontWeight:"bold"},{tag:T.emphasis,fontStyle:"italic"},{tag:T.strong,fontWeight:"bold"},{tag:T.strikethrough,textDecoration:"line-through"},{tag:T.keyword,color:"#708"},{tag:[T.atom,T.bool,T.url,T.contentSeparator,T.labelName],color:"#219"},{tag:[T.literal,T.inserted],color:"#164"},{tag:[T.string,T.deleted],color:"#a11"},{tag:[T.regexp,T.escape,T.special(T.string)],color:"#e40"},{tag:T.definition(T.variableName),color:"#00f"},{tag:T.local(T.variableName),color:"#30a"},{tag:[T.typeName,T.namespace],color:"#085"},{tag:T.className,color:"#167"},{tag:[T.special(T.variableName),T.macroName],color:"#256"},{tag:T.definition(T.propertyName),color:"#00c"},{tag:T.comment,color:"#940"},{tag:T.invalid,color:"#f00"}]),$e=o.EditorView.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),Je=1e4,Ke="()[]{}",Ye=r.Facet.define({combine:e=>(0,r.combineConfig)(e,{afterCursor:!0,brackets:Ke,maxScanDistance:Je,renderMatch:Qe})}),Ge=o.Decoration.mark({class:"cm-matchingBracket"}),Ze=o.Decoration.mark({class:"cm-nonmatchingBracket"});function Qe(e){let t=[],n=e.matched?Ge:Ze;return t.push(n.range(e.start.from,e.start.to)),e.end&&t.push(n.range(e.end.from,e.end.to)),t}const Xe=[r.StateField.define({create:()=>o.Decoration.none,update(e,t){if(!t.docChanged&&!t.selection)return e;let n=[],i=t.state.facet(Ye);for(let r of t.state.selection.ranges){if(!r.empty)continue;let e=rt(t.state,r.head,-1,i)||r.head>0&&rt(t.state,r.head-1,1,i)||i.afterCursor&&(rt(t.state,r.head,1,i)||r.head<t.state.doc.length&&rt(t.state,r.head+1,-1,i));e&&(n=n.concat(i.renderMatch(e,t.state)))}return o.Decoration.set(n,!0)},provide:e=>o.EditorView.decorations.from(e)}),$e];function et(e={}){return[Ye.of(e),Xe]}const tt=new i.uY;function nt(e,t,n){let r=e.prop(t<0?i.uY.openedBy:i.uY.closedBy);if(r)return r;if(1==e.name.length){let i=n.indexOf(e.name);if(i>-1&&i%2==(t<0?1:0))return[n[i+t]]}return null}function it(e){let t=e.type.prop(tt);return t?t(e.node):e}function rt(e,t,n,i={}){let r=i.maxScanDistance||Je,o=i.brackets||Ke,s=z(e),l=s.resolveInner(t,n);for(let a=l;a;a=a.parent){let i=nt(a.type,n,o);if(i&&a.from<a.to){let r=it(a);if(r&&(n>0?t>=r.from&&t<r.to:t>r.from&&t<=r.to))return ot(e,t,n,a,r,i,o)}}return function(e,t,n,i,r,o,s){let l=n<0?e.sliceDoc(t-1,t):e.sliceDoc(t,t+1),a=s.indexOf(l);if(a<0||a%2==0!=n>0)return null;let c={from:n<0?t-1:t,to:n>0?t+1:t},h=e.doc.iterRange(t,n>0?e.doc.length:0),u=0;for(let f=0;!h.next().done&&f<=o;){let e=h.value;n<0&&(f+=e.length);let o=t+f*n;for(let t=n>0?0:e.length-1,l=n>0?e.length:-1;t!=l;t+=n){let l=s.indexOf(e[t]);if(!(l<0||i.resolveInner(o+t,1).type!=r))if(l%2==0==n>0)u++;else{if(1==u)return{start:c,end:{from:o+t,to:o+t+1},matched:l>>1==a>>1};u--}}n>0&&(f+=e.length)}return h.done?{start:c,matched:!1}:null}(e,t,n,s,l.type,r,o)}function ot(e,t,n,i,r,o,s){let l=i.parent,a={from:r.from,to:r.to},c=0,h=null===l||void 0===l?void 0:l.cursor();if(h&&(n<0?h.childBefore(i.from):h.childAfter(i.to)))do{if(n<0?h.to<=i.from:h.from>=i.to){if(0==c&&o.indexOf(h.type.name)>-1&&h.from<h.to){let e=it(h);return{start:a,end:e?{from:e.from,to:e.to}:void 0,matched:!0}}if(nt(h.type,n,s))c++;else if(nt(h.type,-n,s)){if(0==c){let e=it(h);return{start:a,end:e&&e.from<e.to?{from:e.from,to:e.to}:void 0,matched:!1}}c--}}}while(n<0?h.prevSibling():h.nextSibling());return{start:a,matched:!1}}const st=Object.create(null),lt=[i.Z6.none],at=[],ct=Object.create(null),ht=Object.create(null);for(let[dt,pt]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])ht[dt]=ft(st,pt);function ut(e,t){at.indexOf(e)>-1||(at.push(e),console.warn(t))}function ft(e,t){let n=[];for(let i of t.split(" ")){let t=[];for(let n of i.split(".")){let i=e[n]||T[n];i?"function"==typeof i?t.length?t=t.map(i):ut(n,`Modifier ${n} used at start of tag`):t.length?ut(n,`Tag ${n} used as modifier`):t=Array.isArray(i)?i:[i]:ut(n,`Unknown highlighting tag ${n}`)}for(let e of t)n.push(e)}if(!n.length)return 0;let r=t.replace(/ /g,"_"),o=r+" "+n.map((e=>e.id)),s=ct[o];if(s)return s.id;let l=ct[o]=i.Z6.define({id:lt.length,name:r,props:[h({[r]:n})]});return lt.push(l),l.id}o.Direction.RTL,o.Direction.LTR},203:(e,t,n)=>{n.d(t,{PH:()=>p,Qj:()=>d,Z6:()=>c,fI:()=>h,iX:()=>B,rr:()=>I,uY:()=>s});const i=1024;let r=0;class o{constructor(e,t){this.from=e,this.to=t}}class s{constructor(e={}){this.id=r++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=c.match(e)),t=>{let n=e(t);return void 0===n?null:[this,n]}}}s.closedBy=new s({deserialize:e=>e.split(" ")}),s.openedBy=new s({deserialize:e=>e.split(" ")}),s.group=new s({deserialize:e=>e.split(" ")}),s.isolate=new s({deserialize:e=>{if(e&&"rtl"!=e&&"ltr"!=e&&"auto"!=e)throw new RangeError("Invalid value for isolate: "+e);return e||"auto"}}),s.contextHash=new s({perNode:!0}),s.lookAhead=new s({perNode:!0}),s.mounted=new s({perNode:!0});class l{constructor(e,t,n){this.tree=e,this.overlay=t,this.parser=n}static get(e){return e&&e.props&&e.props[s.mounted.id]}}const a=Object.create(null);class c{constructor(e,t,n,i=0){this.name=e,this.props=t,this.id=n,this.flags=i}static define(e){let t=e.props&&e.props.length?Object.create(null):a,n=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),i=new c(e.name||"",t,e.id,n);if(e.props)for(let r of e.props)if(Array.isArray(r)||(r=r(i)),r){if(r[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");t[r[0].id]=r[1]}return i}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(s.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let n in e)for(let i of n.split(" "))t[i]=e[n];return e=>{for(let n=e.prop(s.group),i=-1;i<(n?n.length:0);i++){let r=t[i<0?e.name:n[i]];if(r)return r}}}}c.none=new c("",Object.create(null),0,8);class h{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let n of this.types){let i=null;for(let t of e){let e=t(n);e&&(i||(i=Object.assign({},n.props)),i[e[0].id]=e[1])}t.push(i?new c(n.name,i,n.id,n.flags):n)}return new h(t)}}const u=new WeakMap,f=new WeakMap;var d;!function(e){e[e.ExcludeBuffers=1]="ExcludeBuffers",e[e.IncludeAnonymous=2]="IncludeAnonymous",e[e.IgnoreMounts=4]="IgnoreMounts",e[e.IgnoreOverlays=8]="IgnoreOverlays"}(d||(d={}));class p{constructor(e,t,n,i,r){if(this.type=e,this.children=t,this.positions=n,this.length=i,this.props=null,r&&r.length){this.props=Object.create(null);for(let[e,t]of r)this.props["number"==typeof e?e:e.id]=t}}toString(){let e=l.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let n of this.children){let e=n.toString();e&&(t&&(t+=","),t+=e)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new O(this.topNode,e)}cursorAt(e,t=0,n=0){let i=u.get(this)||this.topNode,r=new O(i);return r.moveTo(e,t),u.set(this,r._tree),r}get topNode(){return new w(this,0,0,null)}resolve(e,t=0){let n=v(u.get(this)||this.topNode,e,t,!1);return u.set(this,n),n}resolveInner(e,t=0){let n=v(f.get(this)||this.topNode,e,t,!0);return f.set(this,n),n}resolveStack(e,t=0){return function(e,t,n){let i=e.resolveInner(t,n),r=null;for(let o=i instanceof w?i:i.context.parent;o;o=o.parent)if(o.index<0){let e=o.parent;(r||(r=[i])).push(e.resolve(t,n)),o=e}else{let e=l.get(o.tree);if(e&&e.overlay&&e.overlay[0].from<=t&&e.overlay[e.overlay.length-1].to>=t){let s=new w(e.tree,e.overlay[0].from+o.from,-1,o);(r||(r=[i])).push(v(s,t,n,!1))}}return r?A(r):i}(this,e,t)}iterate(e){let{enter:t,leave:n,from:i=0,to:r=this.length}=e,o=e.mode||0,s=(o&d.IncludeAnonymous)>0;for(let l=this.cursor(o|d.IncludeAnonymous);;){let e=!1;if(l.from<=r&&l.to>=i&&(!s&&l.type.isAnonymous||!1!==t(l))){if(l.firstChild())continue;e=!0}for(;e&&n&&(s||!l.type.isAnonymous)&&n(l),!l.nextSibling();){if(!l.parent())return;e=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:T(c.none,this.children,this.positions,0,this.children.length,0,this.length,((e,t,n)=>new p(this.type,e,t,n,this.propValues)),e.makeTree||((e,t,n)=>new p(c.none,e,t,n)))}static build(e){return function(e){var t;let{buffer:n,nodeSet:r,maxBufferLength:o=i,reused:l=[],minRepeatType:a=r.types.length}=e,c=Array.isArray(n)?new m(n,n.length):n,h=r.types,u=0,f=0;function d(e,t,n,i,s,p){let{id:m,start:S,end:C,size:A}=c,E=f,O=u;for(;A<0;){if(c.next(),-1==A){let t=l[m];return n.push(t),void i.push(S-e)}if(-3==A)return void(u=m);if(-4==A)return void(f=m);throw new RangeError(`Unrecognized record size: ${A}`)}let D,M,P=h[m],I=S-e;if(C-S<=o&&(M=x(c.pos-t,s))){let t=new Uint16Array(M.size-M.skip),n=c.pos-M.size,i=t.length;for(;c.pos>n;)i=k(M.start,t,i);D=new g(t,C-M.start,r),I=M.start-e}else{let e=c.pos-A;c.next();let t=[],n=[],i=m>=a?m:-1,r=0,s=C;for(;c.pos>e;)i>=0&&c.id==i&&c.size>=0?(c.end<=s-o&&(b(t,n,S,r,c.end,s,i,E,O),r=t.length,s=c.end),c.next()):p>2500?y(S,e,t,n):d(S,e,t,n,i,p+1);if(i>=0&&r>0&&r<t.length&&b(t,n,S,r,S,s,i,E,O),t.reverse(),n.reverse(),i>-1&&r>0){let e=v(P,O);D=T(P,t,n,0,t.length,0,C-S,e,e)}else D=w(P,t,n,C-S,E-C,O)}n.push(D),i.push(I)}function y(e,t,n,i){let s=[],l=0,a=-1;for(;c.pos>t;){let{id:e,start:t,end:n,size:i}=c;if(i>4)c.next();else{if(a>-1&&t<a)break;a<0&&(a=n-o),s.push(e,t,n),l++,c.next()}}if(l){let t=new Uint16Array(4*l),o=s[s.length-2];for(let e=s.length-3,n=0;e>=0;e-=3)t[n++]=s[e],t[n++]=s[e+1]-o,t[n++]=s[e+2]-o,t[n++]=n;n.push(new g(t,s[2]-o,r)),i.push(o-e)}}function v(e,t){return(n,i,r)=>{let o,l,a=0,c=n.length-1;if(c>=0&&(o=n[c])instanceof p){if(!c&&o.type==e&&o.length==r)return o;(l=o.prop(s.lookAhead))&&(a=i[c]+o.length+l)}return w(e,n,i,r,a,t)}}function b(e,t,n,i,o,s,l,a,c){let h=[],u=[];for(;e.length>i;)h.push(e.pop()),u.push(t.pop()+n-o);e.push(w(r.types[l],h,u,s-o,a-s,c)),t.push(o-n)}function w(e,t,n,i,r,o,l){if(o){let e=[s.contextHash,o];l=l?[e].concat(l):[e]}if(r>25){let e=[s.lookAhead,r];l=l?[e].concat(l):[e]}return new p(e,t,n,i,l)}function x(e,t){let n=c.fork(),i=0,r=0,s=0,l=n.end-o,h={size:0,start:0,skip:0};e:for(let o=n.pos-e;n.pos>o;){let e=n.size;if(n.id==t&&e>=0){h.size=i,h.start=r,h.skip=s,s+=4,i+=4,n.next();continue}let c=n.pos-e;if(e<0||c<o||n.start<l)break;let u=n.id>=a?4:0,f=n.start;for(n.next();n.pos>c;){if(n.size<0){if(-3!=n.size)break e;u+=4}else n.id>=a&&(u+=4);n.next()}r=f,i+=e,s+=u}return(t<0||i==e)&&(h.size=i,h.start=r,h.skip=s),h.size>4?h:void 0}function k(e,t,n){let{id:i,start:r,end:o,size:s}=c;if(c.next(),s>=0&&i<a){let l=n;if(s>4){let i=c.pos-(s-4);for(;c.pos>i;)n=k(e,t,n)}t[--n]=l,t[--n]=o-e,t[--n]=r-e,t[--n]=i}else-3==s?u=i:-4==s&&(f=i);return n}let S=[],C=[];for(;c.pos>0;)d(e.start||0,e.bufferStart||0,S,C,-1,0);let A=null!==(t=e.length)&&void 0!==t?t:S.length?C[0]+S[0].length:0;return new p(h[e.topID],S.reverse(),C.reverse(),A)}(e)}}p.empty=new p(c.none,[],[],0);class m{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new m(this.buffer,this.index)}}class g{constructor(e,t,n){this.buffer=e,this.length=t,this.set=n}get type(){return c.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],n=this.buffer[e+3],i=this.set.types[t],r=i.name;if(/\W/.test(r)&&!i.isError&&(r=JSON.stringify(r)),n==(e+=4))return r;let o=[];for(;e<n;)o.push(this.childString(e)),e=this.buffer[e+3];return r+"("+o.join(",")+")"}findChild(e,t,n,i,r){let{buffer:o}=this,s=-1;for(let l=e;l!=t&&!(y(r,i,o[l+1],o[l+2])&&(s=l,n>0));l=o[l+3]);return s}slice(e,t,n){let i=this.buffer,r=new Uint16Array(t-e),o=0;for(let s=e,l=0;s<t;){r[l++]=i[s++],r[l++]=i[s++]-n;let t=r[l++]=i[s++]-n;r[l++]=i[s++]-e,o=Math.max(o,t)}return new g(r,o,this.set)}}function y(e,t,n,i){switch(e){case-2:return n<t;case-1:return i>=t&&n<t;case 0:return n<t&&i>t;case 1:return n<=t&&i>t;case 2:return i>t;case 4:return!0}}function v(e,t,n,i){for(var r;e.from==e.to||(n<1?e.from>=t:e.from>t)||(n>-1?e.to<=t:e.to<t);){let t=!i&&e instanceof w&&e.index<0?null:e.parent;if(!t)return e;e=t}let o=i?0:d.IgnoreOverlays;if(i)for(let s=e,l=s.parent;l;s=l,l=s.parent)s instanceof w&&s.index<0&&(null===(r=l.enter(t,n,o))||void 0===r?void 0:r.from)!=s.from&&(e=l);for(;;){let i=e.enter(t,n,o);if(!i)return e;e=i}}class b{cursor(e=0){return new O(this,e)}getChild(e,t=null,n=null){let i=x(this,e,t,n);return i.length?i[0]:null}getChildren(e,t=null,n=null){return x(this,e,t,n)}resolve(e,t=0){return v(this,e,t,!1)}resolveInner(e,t=0){return v(this,e,t,!0)}matchContext(e){return k(this.parent,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),n=this;for(;t;){let e=t.lastChild;if(!e||e.to!=t.to)break;e.type.isError&&e.from==e.to?(n=t,t=e.prevSibling):t=e}return n}get node(){return this}get next(){return this.parent}}class w extends b{constructor(e,t,n,i){super(),this._tree=e,this.from=t,this.index=n,this._parent=i}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,n,i,r=0){for(let o=this;;){for(let{children:s,positions:a}=o._tree,c=t>0?s.length:-1;e!=c;e+=t){let c=s[e],h=a[e]+o.from;if(y(i,n,h,h+c.length))if(c instanceof g){if(r&d.ExcludeBuffers)continue;let s=c.findChild(0,c.buffer.length,t,n-h,i);if(s>-1)return new C(new S(o,c,e,h),null,s)}else if(r&d.IncludeAnonymous||!c.type.isAnonymous||D(c)){let s;if(!(r&d.IgnoreMounts)&&(s=l.get(c))&&!s.overlay)return new w(s.tree,h,e,o);let a=new w(c,h,e,o);return r&d.IncludeAnonymous||!a.type.isAnonymous?a:a.nextChild(t<0?c.children.length-1:0,t,n,i)}}if(r&d.IncludeAnonymous||!o.type.isAnonymous)return null;if(e=o.index>=0?o.index+t:t<0?-1:o._parent._tree.children.length,o=o._parent,!o)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,n=0){let i;if(!(n&d.IgnoreOverlays)&&(i=l.get(this._tree))&&i.overlay){let n=e-this.from;for(let{from:e,to:r}of i.overlay)if((t>0?e<=n:e<n)&&(t<0?r>=n:r>n))return new w(i.tree,i.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,n)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function x(e,t,n,i){let r=e.cursor(),o=[];if(!r.firstChild())return o;if(null!=n)for(let s=!1;!s;)if(s=r.type.is(n),!r.nextSibling())return o;for(;;){if(null!=i&&r.type.is(i))return o;if(r.type.is(t)&&o.push(r.node),!r.nextSibling())return null==i?o:[]}}function k(e,t,n=t.length-1){for(let i=e;n>=0;i=i.parent){if(!i)return!1;if(!i.type.isAnonymous){if(t[n]&&t[n]!=i.name)return!1;n--}}return!0}class S{constructor(e,t,n,i){this.parent=e,this.buffer=t,this.index=n,this.start=i}}class C extends b{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,n){super(),this.context=e,this._parent=t,this.index=n,this.type=e.buffer.set.types[e.buffer.buffer[n]]}child(e,t,n){let{buffer:i}=this.context,r=i.findChild(this.index+4,i.buffer[this.index+3],e,t-this.context.start,n);return r<0?null:new C(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,n=0){if(n&d.ExcludeBuffers)return null;let{buffer:i}=this.context,r=i.findChild(this.index+4,i.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return r<0?null:new C(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new C(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new C(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:n}=this.context,i=this.index+4,r=n.buffer[this.index+3];if(r>i){let o=n.buffer[this.index+1];e.push(n.slice(i,r,o)),t.push(0)}return new p(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function A(e){if(!e.length)return null;let t=0,n=e[0];for(let o=1;o<e.length;o++){let i=e[o];(i.from>n.from||i.to<n.to)&&(n=i,t=o)}let i=n instanceof w&&n.index<0?null:n.parent,r=e.slice();return i?r[t]=i:r.splice(t,1),new E(r,n)}class E{constructor(e,t){this.heads=e,this.node=t}get next(){return A(this.heads)}}class O{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof w)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:n,buffer:i}=this.buffer;return this.type=t||i.set.types[i.buffer[e]],this.from=n+i.buffer[e+1],this.to=n+i.buffer[e+2],!0}yield(e){return!!e&&(e instanceof w?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,n){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,n,this.mode));let{buffer:i}=this.buffer,r=i.findChild(this.index+4,i.buffer[this.index+3],e,t-this.buffer.start,n);return!(r<0)&&(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,n=this.mode){return this.buffer?!(n&d.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,n))}parent(){if(!this.buffer)return this.yieldNode(this.mode&d.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&d.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,n=this.stack.length-1;if(e<0){let e=n<0?0:this.stack[n]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(n<0?t.buffer.length:t.buffer[this.stack[n]+3]))return this.yieldBuf(e)}return n<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,n,{buffer:i}=this;if(i){if(e>0){if(this.index<i.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(i.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:n}=i)}else({index:t,_parent:n}=this._tree);for(;n;({index:t,_parent:n}=n))if(t>-1)for(let i=t+e,r=e<0?-1:n._tree.children.length;i!=r;i+=e){let e=n._tree.children[i];if(this.mode&d.IncludeAnonymous||e instanceof g||!e.type.isAnonymous||D(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,n=0;if(e&&e.context==this.buffer)e:for(let i=this.index,r=this.stack.length;r>=0;){for(let o=e;o;o=o._parent)if(o.index==i){if(i==this.index)return o;t=o,n=r+1;break e}i=this.stack[--r]}for(let i=n;i<this.stack.length;i++)t=new C(this.buffer,t,this.stack[i]);return this.bufferNode=new C(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let n=0;;){let i=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){n++;continue}this.type.isAnonymous||(i=!0)}for(;;){if(i&&t&&t(this),i=this.type.isAnonymous,!n)return;if(this.nextSibling())break;this.parent(),n--,i=!0}}}matchContext(e){if(!this.buffer)return k(this.node.parent,e);let{buffer:t}=this.buffer,{types:n}=t.set;for(let i=e.length-1,r=this.stack.length-1;i>=0;r--){if(r<0)return k(this._tree,e,i);let o=n[t.buffer[this.stack[r]]];if(!o.isAnonymous){if(e[i]&&e[i]!=o.name)return!1;i--}}return!0}}function D(e){return e.children.some((e=>e instanceof g||!e.type.isAnonymous||D(e)))}const M=new WeakMap;function P(e,t){if(!e.isAnonymous||t instanceof g||t.type!=e)return 1;let n=M.get(t);if(null==n){n=1;for(let i of t.children){if(i.type!=e||!(i instanceof p)){n=1;break}n+=P(e,i)}M.set(t,n)}return n}function T(e,t,n,i,r,o,s,l,a){let c=0;for(let d=i;d<r;d++)c+=P(e,t[d]);let h=Math.ceil(1.5*c/8),u=[],f=[];return function t(n,i,r,s,l){for(let c=r;c<s;){let r=c,d=i[c],p=P(e,n[c]);for(c++;c<s;c++){let t=P(e,n[c]);if(p+t>=h)break;p+=t}if(c==r+1){if(p>h){let e=n[r];t(e.children,e.positions,0,e.children.length,i[r]+l);continue}u.push(n[r])}else{let t=i[c-1]+n[c-1].length-d;u.push(T(e,n,i,r,c,d,t,null,a))}f.push(d+l-o)}}(t,n,i,r,0),(l||a)(u,f,s)}class I{constructor(e,t,n,i,r=!1,o=!1){this.from=e,this.to=t,this.tree=n,this.offset=i,this.open=(r?1:0)|(o?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(e,t=[],n=!1){let i=[new I(0,e.length,e,0,!1,n)];for(let r of t)r.to>e.length&&i.push(r);return i}static applyChanges(e,t,n=128){if(!t.length)return e;let i=[],r=1,o=e.length?e[0]:null;for(let s=0,l=0,a=0;;s++){let c=s<t.length?t[s]:null,h=c?c.fromA:1e9;if(h-l>=n)for(;o&&o.from<h;){let t=o;if(l>=t.from||h<=t.to||a){let e=Math.max(t.from,l)-a,n=Math.min(t.to,h)-a;t=e>=n?null:new I(e,n,t.tree,t.offset+a,s>0,!!c)}if(t&&i.push(t),o.to>h)break;o=r<e.length?e[r++]:null}if(!c)break;l=c.toA,a=c.toA-c.toB}return i}}class B{startParse(e,t,n){return"string"==typeof e&&(e=new N(e)),n=n?n.length?n.map((e=>new o(e.from,e.to))):[new o(0,0)]:[new o(0,e.length)],this.createParse(e,t||[],n)}parse(e,t,n){let i=this.startParse(e,t,n);for(;;){let e=i.advance();if(e)return e}}}class N{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new s({perNode:!0})},369:(e,t,n)=>{n.d(t,{m:()=>i});var i=function(e){return{line:e.state.doc.lineAt(e.state.selection.main.from),lineCount:e.state.doc.lines,lineBreak:e.state.lineBreak,length:e.state.doc.length,readOnly:e.state.readOnly,tabSize:e.state.tabSize,selection:e.state.selection,selectionAsSingle:e.state.selection.asSingle().main,ranges:e.state.selection.ranges,selectionCode:e.state.sliceDoc(e.state.selection.main.from,e.state.selection.main.to),selections:e.state.selection.ranges.map((function(t){return e.state.sliceDoc(t.from,t.to)})),selectedText:e.state.selection.ranges.some((function(e){return!e.empty}))}}},442:t=>{t.exports=e},644:(e,t,n)=>{function i(e,t){if(null==e)return{};var n,i,r=function(e,t){if(null==e)return{};var n={};for(var i in e)if({}.hasOwnProperty.call(e,i)){if(-1!==t.indexOf(i))continue;n[i]=e[i]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)n=o[i],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}n.d(t,{A:()=>i})},687:(e,t,n)=>{n.d(t,{o:()=>zt,V:()=>Ut});var i=n(730),r=n(60),o=n(720);function s(){var e=arguments[0];"string"==typeof e&&(e=document.createElement(e));var t=1,n=arguments[1];if(n&&"object"==typeof n&&null==n.nodeType&&!Array.isArray(n)){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var r=n[i];"string"==typeof r?e.setAttribute(i,r):null!=r&&(e[i]=r)}t++}for(;t<arguments.length;t++)l(e,arguments[t]);return e}function l(e,t){if("string"==typeof t)e.appendChild(document.createTextNode(t));else if(null==t);else if(null!=t.nodeType)e.appendChild(t);else{if(!Array.isArray(t))throw new RangeError("Unsupported child node: "+t);for(var n=0;n<t.length;n++)l(e,t[n])}}const a="function"==typeof String.prototype.normalize?e=>e.normalize("NFKD"):e=>e;class c{constructor(e,t,n=0,i=e.length,r,o){this.test=o,this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=e.iterRange(n,i),this.bufferStart=n,this.normalize=r?e=>r(a(e)):a,this.query=this.normalize(t)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return-1;this.bufferPos=0,this.buffer=this.iter.value}return(0,r.codePointAt)(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let e=this.peek();if(e<0)return this.done=!0,this;let t=(0,r.fromCodePoint)(e),n=this.bufferStart+this.bufferPos;this.bufferPos+=(0,r.codePointSize)(e);let i=this.normalize(t);if(i.length)for(let r=0,o=n;;r++){let e=i.charCodeAt(r),s=this.match(e,o,this.bufferPos+this.bufferStart);if(r==i.length-1){if(s)return this.value=s,this;break}o==n&&r<t.length&&t.charCodeAt(r)==e&&o++}}}match(e,t,n){let i=null;for(let r=0;r<this.matches.length;r+=2){let t=this.matches[r],o=!1;this.query.charCodeAt(t)==e&&(t==this.query.length-1?i={from:this.matches[r+1],to:n}:(this.matches[r]++,o=!0)),o||(this.matches.splice(r,2),r-=2)}return this.query.charCodeAt(0)==e&&(1==this.query.length?i={from:t,to:n}:this.matches.push(1,t)),i&&this.test&&!this.test(i.from,i.to,this.buffer,this.bufferStart)&&(i=null),i}}"undefined"!=typeof Symbol&&(c.prototype[Symbol.iterator]=function(){return this});const h={from:-1,to:-1,match:/.*/.exec("")},u="gm"+(null==/x/.unicode?"":"u");class f{constructor(e,t,n,i=0,r=e.length){if(this.text=e,this.to=r,this.curLine="",this.done=!1,this.value=h,/\\[sWDnr]|\n|\r|\[\^/.test(t))return new m(e,t,n,i,r);this.re=new RegExp(t,u+((null===n||void 0===n?void 0:n.ignoreCase)?"i":"")),this.test=null===n||void 0===n?void 0:n.test,this.iter=e.iter();let o=e.lineAt(i);this.curLineStart=o.from,this.matchPos=g(e,i),this.getLine(this.curLineStart)}getLine(e){this.iter.next(e),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let e=this.matchPos-this.curLineStart;;){this.re.lastIndex=e;let t=this.matchPos<=this.to&&this.re.exec(this.curLine);if(t){let n=this.curLineStart+t.index,i=n+t[0].length;if(this.matchPos=g(this.text,i+(n==i?1:0)),n==this.curLineStart+this.curLine.length&&this.nextLine(),(n<i||n>this.value.to)&&(!this.test||this.test(n,i,t)))return this.value={from:n,to:i,match:t},this;e=this.matchPos-this.curLineStart}else{if(!(this.curLineStart+this.curLine.length<this.to))return this.done=!0,this;this.nextLine(),e=0}}}}const d=new WeakMap;class p{constructor(e,t){this.from=e,this.text=t}get to(){return this.from+this.text.length}static get(e,t,n){let i=d.get(e);if(!i||i.from>=n||i.to<=t){let i=new p(t,e.sliceString(t,n));return d.set(e,i),i}if(i.from==t&&i.to==n)return i;let{text:r,from:o}=i;return o>t&&(r=e.sliceString(t,o)+r,o=t),i.to<n&&(r+=e.sliceString(i.to,n)),d.set(e,new p(o,r)),new p(t,r.slice(t-o,n-o))}}class m{constructor(e,t,n,i,r){this.text=e,this.to=r,this.done=!1,this.value=h,this.matchPos=g(e,i),this.re=new RegExp(t,u+((null===n||void 0===n?void 0:n.ignoreCase)?"i":"")),this.test=null===n||void 0===n?void 0:n.test,this.flat=p.get(e,i,this.chunkEnd(i+5e3))}chunkEnd(e){return e>=this.to?this.to:this.text.lineAt(e).to}next(){for(;;){let e=this.re.lastIndex=this.matchPos-this.flat.from,t=this.re.exec(this.flat.text);if(t&&!t[0]&&t.index==e&&(this.re.lastIndex=e+1,t=this.re.exec(this.flat.text)),t){let e=this.flat.from+t.index,n=e+t[0].length;if((this.flat.to>=this.to||t.index+t[0].length<=this.flat.text.length-10)&&(!this.test||this.test(e,n,t)))return this.value={from:e,to:n,match:t},this.matchPos=g(this.text,n+(e==n?1:0)),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=p.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+2*this.flat.text.length))}}}function g(e,t){if(t>=e.length)return t;let n,i=e.lineAt(t);for(;t<i.to&&(n=i.text.charCodeAt(t-i.from))>=56320&&n<57344;)t++;return t}function y(e){let t=s("input",{class:"cm-textfield",name:"line",value:String(e.state.doc.lineAt(e.state.selection.main.head).number)});function n(){let n=/^([+-])?(\d+)?(:\d+)?(%)?$/.exec(t.value);if(!n)return;let{state:o}=e,s=o.doc.lineAt(o.selection.main.head),[,l,a,c,h]=n,u=c?+c.slice(1):0,f=a?+a:s.number;if(a&&h){let e=f/100;l&&(e=e*("-"==l?-1:1)+s.number/o.doc.lines),f=Math.round(o.doc.lines*e)}else a&&l&&(f=f*("-"==l?-1:1)+s.number);let d=o.doc.line(Math.max(1,Math.min(o.doc.lines,f))),p=r.EditorSelection.cursor(d.from+Math.max(0,Math.min(u,d.length)));e.dispatch({effects:[v.of(!1),i.EditorView.scrollIntoView(p.from,{y:"center"})],selection:p}),e.focus()}return{dom:s("form",{class:"cm-gotoLine",onkeydown:t=>{27==t.keyCode?(t.preventDefault(),e.dispatch({effects:v.of(!1)}),e.focus()):13==t.keyCode&&(t.preventDefault(),n())},onsubmit:e=>{e.preventDefault(),n()}},s("label",e.state.phrase("Go to line"),": ",t)," ",s("button",{class:"cm-button",type:"submit"},e.state.phrase("go")),s("button",{name:"close",onclick:()=>{e.dispatch({effects:v.of(!1)}),e.focus()},"aria-label":e.state.phrase("close"),type:"button"},["\xd7"]))}}"undefined"!=typeof Symbol&&(f.prototype[Symbol.iterator]=m.prototype[Symbol.iterator]=function(){return this});const v=r.StateEffect.define(),b=r.StateField.define({create:()=>!0,update(e,t){for(let n of t.effects)n.is(v)&&(e=n.value);return e},provide:e=>i.showPanel.from(e,(e=>e?y:null))}),w=i.EditorView.baseTheme({".cm-panel.cm-gotoLine":{padding:"2px 6px 4px",position:"relative","& label":{fontSize:"80%"},"& [name=close]":{position:"absolute",top:"0",bottom:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:"0"}}}),x={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},k=r.Facet.define({combine:e=>(0,r.combineConfig)(e,x,{highlightWordAroundCursor:(e,t)=>e||t,minSelectionLength:Math.min,maxMatches:Math.min})});const S=i.Decoration.mark({class:"cm-selectionMatch"}),C=i.Decoration.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function A(e,t,n,i){return(0==n||e(t.sliceDoc(n-1,n))!=r.CharCategory.Word)&&(i==t.doc.length||e(t.sliceDoc(i,i+1))!=r.CharCategory.Word)}const E=i.ViewPlugin.fromClass(class{constructor(e){this.decorations=this.getDeco(e)}update(e){(e.selectionSet||e.docChanged||e.viewportChanged)&&(this.decorations=this.getDeco(e.view))}getDeco(e){let t=e.state.facet(k),{state:n}=e,o=n.selection;if(o.ranges.length>1)return i.Decoration.none;let s,l=o.main,a=null;if(l.empty){if(!t.highlightWordAroundCursor)return i.Decoration.none;let e=n.wordAt(l.head);if(!e)return i.Decoration.none;a=n.charCategorizer(l.head),s=n.sliceDoc(e.from,e.to)}else{let e=l.to-l.from;if(e<t.minSelectionLength||e>200)return i.Decoration.none;if(t.wholeWords){if(s=n.sliceDoc(l.from,l.to),a=n.charCategorizer(l.head),!A(a,n,l.from,l.to)||!function(e,t,n,i){return e(t.sliceDoc(n,n+1))==r.CharCategory.Word&&e(t.sliceDoc(i-1,i))==r.CharCategory.Word}(a,n,l.from,l.to))return i.Decoration.none}else if(s=n.sliceDoc(l.from,l.to),!s)return i.Decoration.none}let h=[];for(let r of e.visibleRanges){let e=new c(n.doc,s,r.from,r.to);for(;!e.next().done;){let{from:r,to:o}=e.value;if((!a||A(a,n,r,o))&&(l.empty&&r<=l.from&&o>=l.to?h.push(C.range(r,o)):(r>=l.to||o<=l.from)&&h.push(S.range(r,o)),h.length>t.maxMatches))return i.Decoration.none}}return i.Decoration.set(h)}},{decorations:e=>e.decorations}),O=i.EditorView.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}});const D=r.Facet.define({combine:e=>(0,r.combineConfig)(e,{top:!1,caseSensitive:!1,literal:!1,regexp:!1,wholeWord:!1,createPanel:e=>new ie(e),scrollToMatch:e=>i.EditorView.scrollIntoView(e)})});class M{constructor(e){this.search=e.search,this.caseSensitive=!!e.caseSensitive,this.literal=!!e.literal,this.regexp=!!e.regexp,this.replace=e.replace||"",this.valid=!!this.search&&(!this.regexp||function(e){try{return new RegExp(e,u),!0}catch(t){return!1}}(this.search)),this.unquoted=this.unquote(this.search),this.wholeWord=!!e.wholeWord}unquote(e){return this.literal?e:e.replace(/\\([nrt\\])/g,((e,t)=>"n"==t?"\n":"r"==t?"\r":"t"==t?"\t":"\\"))}eq(e){return this.search==e.search&&this.replace==e.replace&&this.caseSensitive==e.caseSensitive&&this.regexp==e.regexp&&this.wholeWord==e.wholeWord}create(){return this.regexp?new L(this):new I(this)}getCursor(e,t=0,n){let i=e.doc?e:r.EditorState.create({doc:e});return null==n&&(n=i.doc.length),this.regexp?B(this,i,t,n):T(this,i,t,n)}}class P{constructor(e){this.spec=e}}function T(e,t,n,i){return new c(t.doc,e.unquoted,n,i,e.caseSensitive?void 0:e=>e.toLowerCase(),e.wholeWord?(o=t.doc,s=t.charCategorizer(t.selection.main.head),(e,t,n,i)=>((i>e||i+n.length<t)&&(i=Math.max(0,e-2),n=o.sliceString(i,Math.min(o.length,t+2))),(s(N(n,e-i))!=r.CharCategory.Word||s(R(n,e-i))!=r.CharCategory.Word)&&(s(R(n,t-i))!=r.CharCategory.Word||s(N(n,t-i))!=r.CharCategory.Word))):void 0);var o,s}class I extends P{constructor(e){super(e)}nextMatch(e,t,n){let i=T(this.spec,e,n,e.doc.length).nextOverlapping();if(i.done){let n=Math.min(e.doc.length,t+this.spec.unquoted.length);i=T(this.spec,e,0,n).nextOverlapping()}return i.done||i.value.from==t&&i.value.to==n?null:i.value}prevMatchInRange(e,t,n){for(let i=n;;){let n=Math.max(t,i-1e4-this.spec.unquoted.length),r=T(this.spec,e,n,i),o=null;for(;!r.nextOverlapping().done;)o=r.value;if(o)return o;if(n==t)return null;i-=1e4}}prevMatch(e,t,n){let i=this.prevMatchInRange(e,0,t);return i||(i=this.prevMatchInRange(e,Math.max(0,n-this.spec.unquoted.length),e.doc.length)),!i||i.from==t&&i.to==n?null:i}getReplacement(e){return this.spec.unquote(this.spec.replace)}matchAll(e,t){let n=T(this.spec,e,0,e.doc.length),i=[];for(;!n.next().done;){if(i.length>=t)return null;i.push(n.value)}return i}highlight(e,t,n,i){let r=T(this.spec,e,Math.max(0,t-this.spec.unquoted.length),Math.min(n+this.spec.unquoted.length,e.doc.length));for(;!r.next().done;)i(r.value.from,r.value.to)}}function B(e,t,n,i){return new f(t.doc,e.search,{ignoreCase:!e.caseSensitive,test:e.wholeWord?(o=t.charCategorizer(t.selection.main.head),(e,t,n)=>!n[0].length||(o(N(n.input,n.index))!=r.CharCategory.Word||o(R(n.input,n.index))!=r.CharCategory.Word)&&(o(R(n.input,n.index+n[0].length))!=r.CharCategory.Word||o(N(n.input,n.index+n[0].length))!=r.CharCategory.Word)):void 0},n,i);var o}function N(e,t){return e.slice((0,r.findClusterBreak)(e,t,!1),t)}function R(e,t){return e.slice(t,(0,r.findClusterBreak)(e,t))}class L extends P{nextMatch(e,t,n){let i=B(this.spec,e,n,e.doc.length).next();return i.done&&(i=B(this.spec,e,0,t).next()),i.done?null:i.value}prevMatchInRange(e,t,n){for(let i=1;;i++){let r=Math.max(t,n-1e4*i),o=B(this.spec,e,r,n),s=null;for(;!o.next().done;)s=o.value;if(s&&(r==t||s.from>r+10))return s;if(r==t)return null}}prevMatch(e,t,n){return this.prevMatchInRange(e,0,t)||this.prevMatchInRange(e,n,e.doc.length)}getReplacement(e){return this.spec.unquote(this.spec.replace).replace(/\$([$&]|\d+)/g,((t,n)=>{if("&"==n)return e.match[0];if("$"==n)return"$";for(let i=n.length;i>0;i--){let t=+n.slice(0,i);if(t>0&&t<e.match.length)return e.match[t]+n.slice(i)}return t}))}matchAll(e,t){let n=B(this.spec,e,0,e.doc.length),i=[];for(;!n.next().done;){if(i.length>=t)return null;i.push(n.value)}return i}highlight(e,t,n,i){let r=B(this.spec,e,Math.max(0,t-250),Math.min(n+250,e.doc.length));for(;!r.next().done;)i(r.value.from,r.value.to)}}const j=r.StateEffect.define(),F=r.StateEffect.define(),W=r.StateField.define({create:e=>new V(Z(e).create(),null),update(e,t){for(let n of t.effects)n.is(j)?e=new V(n.value.create(),e.panel):n.is(F)&&(e=new V(e.query,n.value?G:null));return e},provide:e=>i.showPanel.from(e,(e=>e.panel))});class V{constructor(e,t){this.query=e,this.panel=t}}const q=i.Decoration.mark({class:"cm-searchMatch"}),_=i.Decoration.mark({class:"cm-searchMatch cm-searchMatch-selected"}),z=i.ViewPlugin.fromClass(class{constructor(e){this.view=e,this.decorations=this.highlight(e.state.field(W))}update(e){let t=e.state.field(W);(t!=e.startState.field(W)||e.docChanged||e.selectionSet||e.viewportChanged)&&(this.decorations=this.highlight(t))}highlight({query:e,panel:t}){if(!t||!e.spec.valid)return i.Decoration.none;let{view:n}=this,o=new r.RangeSetBuilder;for(let i=0,r=n.visibleRanges,s=r.length;i<s;i++){let{from:t,to:l}=r[i];for(;i<s-1&&l>r[i+1].from-500;)l=r[++i].to;e.highlight(n.state,t,l,((e,t)=>{let i=n.state.selection.ranges.some((n=>n.from==e&&n.to==t));o.add(e,t,i?_:q)}))}return o.finish()}},{decorations:e=>e.decorations});function U(e){return t=>{let n=t.state.field(W,!1);return n&&n.query.spec.valid?e(t,n):ee(t)}}const H=U(((e,{query:t})=>{let{to:n}=e.state.selection.main,i=t.nextMatch(e.state,n,n);if(!i)return!1;let o=r.EditorSelection.single(i.from,i.to),s=e.state.facet(D);return e.dispatch({selection:o,effects:[le(e,i),s.scrollToMatch(o.main,e)],userEvent:"select.search"}),X(e),!0})),$=U(((e,{query:t})=>{let{state:n}=e,{from:i}=n.selection.main,o=t.prevMatch(n,i,i);if(!o)return!1;let s=r.EditorSelection.single(o.from,o.to),l=e.state.facet(D);return e.dispatch({selection:s,effects:[le(e,o),l.scrollToMatch(s.main,e)],userEvent:"select.search"}),X(e),!0})),J=U(((e,{query:t})=>{let n=t.matchAll(e.state,1e3);return!(!n||!n.length)&&(e.dispatch({selection:r.EditorSelection.create(n.map((e=>r.EditorSelection.range(e.from,e.to)))),userEvent:"select.search.matches"}),!0)})),K=U(((e,{query:t})=>{let{state:n}=e,{from:o,to:s}=n.selection.main;if(n.readOnly)return!1;let l=t.nextMatch(n,o,o);if(!l)return!1;let a,c,h=l,u=[],f=[];if(h.from==o&&h.to==s&&(c=n.toText(t.getReplacement(h)),u.push({from:h.from,to:h.to,insert:c}),h=t.nextMatch(n,h.from,h.to),f.push(i.EditorView.announce.of(n.phrase("replaced match on line $",n.doc.lineAt(o).number)+"."))),h){let t=0==u.length||u[0].from>=l.to?0:l.to-l.from-c.length;a=r.EditorSelection.single(h.from-t,h.to-t),f.push(le(e,h)),f.push(n.facet(D).scrollToMatch(a.main,e))}return e.dispatch({changes:u,selection:a,effects:f,userEvent:"input.replace"}),!0})),Y=U(((e,{query:t})=>{if(e.state.readOnly)return!1;let n=t.matchAll(e.state,1e9).map((e=>{let{from:n,to:i}=e;return{from:n,to:i,insert:t.getReplacement(e)}}));if(!n.length)return!1;let r=e.state.phrase("replaced $ matches",n.length)+".";return e.dispatch({changes:n,effects:i.EditorView.announce.of(r),userEvent:"input.replace.all"}),!0}));function G(e){return e.state.facet(D).createPanel(e)}function Z(e,t){var n,i,r,o,s;let l=e.selection.main,a=l.empty||l.to>l.from+100?"":e.sliceDoc(l.from,l.to);if(t&&!a)return t;let c=e.facet(D);return new M({search:(null!==(n=null===t||void 0===t?void 0:t.literal)&&void 0!==n?n:c.literal)?a:a.replace(/\n/g,"\\n"),caseSensitive:null!==(i=null===t||void 0===t?void 0:t.caseSensitive)&&void 0!==i?i:c.caseSensitive,literal:null!==(r=null===t||void 0===t?void 0:t.literal)&&void 0!==r?r:c.literal,regexp:null!==(o=null===t||void 0===t?void 0:t.regexp)&&void 0!==o?o:c.regexp,wholeWord:null!==(s=null===t||void 0===t?void 0:t.wholeWord)&&void 0!==s?s:c.wholeWord})}function Q(e){let t=(0,i.getPanel)(e,G);return t&&t.dom.querySelector("[main-field]")}function X(e){let t=Q(e);t&&t==e.root.activeElement&&t.select()}const ee=e=>{let t=e.state.field(W,!1);if(t&&t.panel){let n=Q(e);if(n&&n!=e.root.activeElement){let i=Z(e.state,t.query.spec);i.valid&&e.dispatch({effects:j.of(i)}),n.focus(),n.select()}}else e.dispatch({effects:[F.of(!0),t?j.of(Z(e.state,t.query.spec)):r.StateEffect.appendConfig.of(ce)]});return!0},te=e=>{let t=e.state.field(W,!1);if(!t||!t.panel)return!1;let n=(0,i.getPanel)(e,G);return n&&n.dom.contains(e.root.activeElement)&&e.focus(),e.dispatch({effects:F.of(!1)}),!0},ne=[{key:"Mod-f",run:ee,scope:"editor search-panel"},{key:"F3",run:H,shift:$,scope:"editor search-panel",preventDefault:!0},{key:"Mod-g",run:H,shift:$,scope:"editor search-panel",preventDefault:!0},{key:"Escape",run:te,scope:"editor search-panel"},{key:"Mod-Shift-l",run:({state:e,dispatch:t})=>{let n=e.selection;if(n.ranges.length>1||n.main.empty)return!1;let{from:i,to:o}=n.main,s=[],l=0;for(let a=new c(e.doc,e.sliceDoc(i,o));!a.next().done;){if(s.length>1e3)return!1;a.value.from==i&&(l=s.length),s.push(r.EditorSelection.range(a.value.from,a.value.to))}return t(e.update({selection:r.EditorSelection.create(s,l),userEvent:"select.search.matches"})),!0}},{key:"Mod-Alt-g",run:e=>{let t=(0,i.getPanel)(e,y);if(!t){let n=[v.of(!0)];null==e.state.field(b,!1)&&n.push(r.StateEffect.appendConfig.of([b,w])),e.dispatch({effects:n}),t=(0,i.getPanel)(e,y)}return t&&t.dom.querySelector("input").select(),!0}},{key:"Mod-d",run:({state:e,dispatch:t})=>{let{ranges:n}=e.selection;if(n.some((e=>e.from===e.to)))return(({state:e,dispatch:t})=>{let{selection:n}=e,i=r.EditorSelection.create(n.ranges.map((t=>e.wordAt(t.head)||r.EditorSelection.cursor(t.head))),n.mainIndex);return!i.eq(n)&&(t(e.update({selection:i})),!0)})({state:e,dispatch:t});let o=e.sliceDoc(n[0].from,n[0].to);if(e.selection.ranges.some((t=>e.sliceDoc(t.from,t.to)!=o)))return!1;let s=function(e,t){let{main:n,ranges:i}=e.selection,r=e.wordAt(n.head),o=r&&r.from==n.from&&r.to==n.to;for(let s=!1,l=new c(e.doc,t,i[i.length-1].to);;){if(l.next(),!l.done){if(s&&i.some((e=>e.from==l.value.from)))continue;if(o){let t=e.wordAt(l.value.from);if(!t||t.from!=l.value.from||t.to!=l.value.to)continue}return l.value}if(s)return null;l=new c(e.doc,t,0,Math.max(0,i[i.length-1].from-1)),s=!0}}(e,o);return!!s&&(t(e.update({selection:e.selection.addRange(r.EditorSelection.range(s.from,s.to),!1),effects:i.EditorView.scrollIntoView(s.to)})),!0)},preventDefault:!0}];class ie{constructor(e){this.view=e;let t=this.query=e.state.field(W).query.spec;function n(e,t,n){return s("button",{class:"cm-button",name:e,onclick:t,type:"button"},n)}this.commit=this.commit.bind(this),this.searchField=s("input",{value:t.search,placeholder:re(e,"Find"),"aria-label":re(e,"Find"),class:"cm-textfield",name:"search",form:"","main-field":"true",onchange:this.commit,onkeyup:this.commit}),this.replaceField=s("input",{value:t.replace,placeholder:re(e,"Replace"),"aria-label":re(e,"Replace"),class:"cm-textfield",name:"replace",form:"",onchange:this.commit,onkeyup:this.commit}),this.caseField=s("input",{type:"checkbox",name:"case",form:"",checked:t.caseSensitive,onchange:this.commit}),this.reField=s("input",{type:"checkbox",name:"re",form:"",checked:t.regexp,onchange:this.commit}),this.wordField=s("input",{type:"checkbox",name:"word",form:"",checked:t.wholeWord,onchange:this.commit}),this.dom=s("div",{onkeydown:e=>this.keydown(e),class:"cm-search"},[this.searchField,n("next",(()=>H(e)),[re(e,"next")]),n("prev",(()=>$(e)),[re(e,"previous")]),n("select",(()=>J(e)),[re(e,"all")]),s("label",null,[this.caseField,re(e,"match case")]),s("label",null,[this.reField,re(e,"regexp")]),s("label",null,[this.wordField,re(e,"by word")]),...e.state.readOnly?[]:[s("br"),this.replaceField,n("replace",(()=>K(e)),[re(e,"replace")]),n("replaceAll",(()=>Y(e)),[re(e,"replace all")])],s("button",{name:"close",onclick:()=>te(e),"aria-label":re(e,"close"),type:"button"},["\xd7"])])}commit(){let e=new M({search:this.searchField.value,caseSensitive:this.caseField.checked,regexp:this.reField.checked,wholeWord:this.wordField.checked,replace:this.replaceField.value});e.eq(this.query)||(this.query=e,this.view.dispatch({effects:j.of(e)}))}keydown(e){(0,i.runScopeHandlers)(this.view,e,"search-panel")?e.preventDefault():13==e.keyCode&&e.target==this.searchField?(e.preventDefault(),(e.shiftKey?$:H)(this.view)):13==e.keyCode&&e.target==this.replaceField&&(e.preventDefault(),K(this.view))}update(e){for(let t of e.transactions)for(let e of t.effects)e.is(j)&&!e.value.eq(this.query)&&this.setQuery(e.value)}setQuery(e){this.query=e,this.searchField.value=e.search,this.replaceField.value=e.replace,this.caseField.checked=e.caseSensitive,this.reField.checked=e.regexp,this.wordField.checked=e.wholeWord}mount(){this.searchField.select()}get pos(){return 80}get top(){return this.view.state.facet(D).top}}function re(e,t){return e.state.phrase(t)}const oe=30,se=/[\s\.,:;?!]/;function le(e,{from:t,to:n}){let r=e.state.doc.lineAt(t),o=e.state.doc.lineAt(n).to,s=Math.max(r.from,t-oe),l=Math.min(o,n+oe),a=e.state.sliceDoc(s,l);if(s!=r.from)for(let i=0;i<oe;i++)if(!se.test(a[i+1])&&se.test(a[i])){a=a.slice(i);break}if(l!=o)for(let i=a.length-1;i>a.length-oe;i--)if(!se.test(a[i-1])&&se.test(a[i])){a=a.slice(0,i);break}return i.EditorView.announce.of(`${e.state.phrase("current match")}. ${a} ${e.state.phrase("on line")} ${r.number}.`)}const ae=i.EditorView.baseTheme({".cm-panel.cm-search":{padding:"2px 6px 4px",position:"relative","& [name=close]":{position:"absolute",top:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:0,margin:0},"& input, & button, & label":{margin:".2em .6em .2em 0"},"& input[type=checkbox]":{marginRight:".2em"},"& label":{fontSize:"80%",whiteSpace:"pre"}},"&light .cm-searchMatch":{backgroundColor:"#ffff0054"},"&dark .cm-searchMatch":{backgroundColor:"#00ffff8a"},"&light .cm-searchMatch-selected":{backgroundColor:"#ff6a0054"},"&dark .cm-searchMatch-selected":{backgroundColor:"#ff00ff8a"}}),ce=[W,r.Prec.low(z),ae];var he=n(194);class ue{constructor(e,t,n,i){this.state=e,this.pos=t,this.explicit=n,this.view=i,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(e){let t=(0,he.mv)(this.state).resolveInner(this.pos,-1);for(;t&&e.indexOf(t.name)<0;)t=t.parent;return t?{from:t.from,to:this.pos,text:this.state.sliceDoc(t.from,this.pos),type:t.type}:null}matchBefore(e){let t=this.state.doc.lineAt(this.pos),n=Math.max(t.from,this.pos-250),i=t.text.slice(n-t.from,this.pos-t.from),r=i.search(ge(e,!1));return r<0?null:{from:n+r,to:this.pos,text:i.slice(r)}}get aborted(){return null==this.abortListeners}addEventListener(e,t,n){"abort"==e&&this.abortListeners&&(this.abortListeners.push(t),n&&n.onDocChange&&(this.abortOnDocChange=!0))}}function fe(e){let t=Object.keys(e).join(""),n=/\w/.test(t);return n&&(t=t.replace(/\w/g,"")),`[${n?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}function de(e){let t=e.map((e=>"string"==typeof e?{label:e}:e)),[n,i]=t.every((e=>/^\w+$/.test(e.label)))?[/\w*$/,/\w+$/]:function(e){let t=Object.create(null),n=Object.create(null);for(let{label:r}of e){t[r[0]]=!0;for(let e=1;e<r.length;e++)n[r[e]]=!0}let i=fe(t)+fe(n)+"*$";return[new RegExp("^"+i),new RegExp(i)]}(t);return e=>{let r=e.matchBefore(i);return r||e.explicit?{from:r?r.from:e.pos,options:t,validFor:n}:null}}class pe{constructor(e,t,n,i){this.completion=e,this.source=t,this.match=n,this.score=i}}function me(e){return e.selection.main.from}function ge(e,t){var n;let{source:i}=e,r=t&&"^"!=i[0],o="$"!=i[i.length-1];return r||o?new RegExp(`${r?"^":""}(?:${i})${o?"$":""}`,null!==(n=e.flags)&&void 0!==n?n:e.ignoreCase?"i":""):e}const ye=r.Annotation.define();const ve=new WeakMap;function be(e){if(!Array.isArray(e))return e;let t=ve.get(e);return t||ve.set(e,t=de(e)),t}const we=r.StateEffect.define(),xe=r.StateEffect.define();class ke{constructor(e){this.pattern=e,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let t=0;t<e.length;){let n=(0,r.codePointAt)(e,t),i=(0,r.codePointSize)(n);this.chars.push(n);let o=e.slice(t,t+i),s=o.toUpperCase();this.folded.push((0,r.codePointAt)(s==o?o.toLowerCase():s,0)),t+=i}this.astral=e.length!=this.chars.length}ret(e,t){return this.score=e,this.matched=t,this}match(e){if(0==this.pattern.length)return this.ret(-100,[]);if(e.length<this.pattern.length)return null;let{chars:t,folded:n,any:i,precise:o,byWord:s}=this;if(1==t.length){let i=(0,r.codePointAt)(e,0),o=(0,r.codePointSize)(i),s=o==e.length?0:-100;if(i==t[0]);else{if(i!=n[0])return null;s+=-200}return this.ret(s,[0,o])}let l=e.indexOf(this.pattern);if(0==l)return this.ret(e.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=t.length,c=0;if(l<0){for(let o=0,s=Math.min(e.length,200);o<s&&c<a;){let s=(0,r.codePointAt)(e,o);s!=t[c]&&s!=n[c]||(i[c++]=o),o+=(0,r.codePointSize)(s)}if(c<a)return null}let h=0,u=0,f=!1,d=0,p=-1,m=-1,g=/[a-z]/.test(e),y=!0;for(let v=0,b=Math.min(e.length,200),w=0;v<b&&u<a;){let i=(0,r.codePointAt)(e,v);l<0&&(h<a&&i==t[h]&&(o[h++]=v),d<a&&(i==t[d]||i==n[d]?(0==d&&(p=v),m=v+1,d++):d=0));let c,b=i<255?i>=48&&i<=57||i>=97&&i<=122?2:i>=65&&i<=90?1:0:(c=(0,r.fromCodePoint)(i))!=c.toLowerCase()?1:c!=c.toUpperCase()?2:0;(!v||1==b&&g||0==w&&0!=b)&&(t[u]==i||n[u]==i&&(f=!0)?s[u++]=v:s.length&&(y=!1)),w=b,v+=(0,r.codePointSize)(i)}return u==a&&0==s[0]&&y?this.result((f?-200:0)-100,s,e):d==a&&0==p?this.ret(-200-e.length+(m==e.length?0:-100),[0,m]):l>-1?this.ret(-700-e.length,[l,l+this.pattern.length]):d==a?this.ret(-900-e.length,[p,m]):u==a?this.result((f?-200:0)-100-700+(y?0:-1100),s,e):2==t.length?null:this.result((i[0]?-700:0)-200-1100,i,e)}result(e,t,n){let i=[],o=0;for(let s of t){let e=s+(this.astral?(0,r.codePointSize)((0,r.codePointAt)(n,s)):1);o&&i[o-1]==s?i[o-1]=e:(i[o++]=s,i[o++]=e)}return this.ret(e-n.length,i)}}class Se{constructor(e){this.pattern=e,this.matched=[],this.score=0,this.folded=e.toLowerCase()}match(e){if(e.length<this.pattern.length)return null;let t=e.slice(0,this.pattern.length),n=t==this.pattern?0:t.toLowerCase()==this.folded?-200:null;return null==n?null:(this.matched=[0,t.length],this.score=n+(e.length==this.pattern.length?0:-100),this)}}const Ce=r.Facet.define({combine:e=>(0,r.combineConfig)(e,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:Ee,filterStrict:!1,compareCompletions:(e,t)=>e.label.localeCompare(t.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(e,t)=>e&&t,closeOnBlur:(e,t)=>e&&t,icons:(e,t)=>e&&t,tooltipClass:(e,t)=>n=>Ae(e(n),t(n)),optionClass:(e,t)=>n=>Ae(e(n),t(n)),addToOptions:(e,t)=>e.concat(t),filterStrict:(e,t)=>e||t})});function Ae(e,t){return e?t?e+" "+t:e:t}function Ee(e,t,n,r,o,s){let l,a,c=e.textDirection==i.Direction.RTL,h=c,u=!1,f="top",d=t.left-o.left,p=o.right-t.right,m=r.right-r.left,g=r.bottom-r.top;if(h&&d<Math.min(m,p)?h=!1:!h&&p<Math.min(m,d)&&(h=!0),m<=(h?d:p))l=Math.max(o.top,Math.min(n.top,o.bottom-g))-t.top,a=Math.min(400,h?d:p);else{u=!0,a=Math.min(400,(c?t.right:o.right-t.left)-30);let e=o.bottom-t.bottom;e>=g||e>t.top?l=n.bottom-t.top:(f="bottom",l=t.bottom-n.top)}return{style:`${f}: ${l/((t.bottom-t.top)/s.offsetHeight)}px; max-width: ${a/((t.right-t.left)/s.offsetWidth)}px`,class:"cm-completionInfo-"+(u?c?"left-narrow":"right-narrow":h?"left":"right")}}function Oe(e,t,n){if(e<=n)return{from:0,to:e};if(t<0&&(t=0),t<=e>>1){let e=Math.floor(t/n);return{from:e*n,to:(e+1)*n}}let i=Math.floor((e-t)/n);return{from:e-(i+1)*n,to:e-i*n}}class De{constructor(e,t,n){this.view=e,this.stateField=t,this.applyCompletion=n,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:e=>this.placeInfo(e),key:this},this.space=null,this.currentClass="";let i=e.state.field(t),{options:r,selected:o}=i.open,s=e.state.facet(Ce);this.optionContent=function(e){let t=e.addToOptions.slice();return e.icons&&t.push({render(e){let t=document.createElement("div");return t.classList.add("cm-completionIcon"),e.type&&t.classList.add(...e.type.split(/\s+/g).map((e=>"cm-completionIcon-"+e))),t.setAttribute("aria-hidden","true"),t},position:20}),t.push({render(e,t,n,i){let r=document.createElement("span");r.className="cm-completionLabel";let o=e.displayLabel||e.label,s=0;for(let l=0;l<i.length;){let e=i[l++],t=i[l++];e>s&&r.appendChild(document.createTextNode(o.slice(s,e)));let n=r.appendChild(document.createElement("span"));n.appendChild(document.createTextNode(o.slice(e,t))),n.className="cm-completionMatchedText",s=t}return s<o.length&&r.appendChild(document.createTextNode(o.slice(s))),r},position:50},{render(e){if(!e.detail)return null;let t=document.createElement("span");return t.className="cm-completionDetail",t.textContent=e.detail,t},position:80}),t.sort(((e,t)=>e.position-t.position)).map((e=>e.render))}(s),this.optionClass=s.optionClass,this.tooltipClass=s.tooltipClass,this.range=Oe(r.length,o,s.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(e.state),this.dom.addEventListener("mousedown",(n=>{let{options:i}=e.state.field(t).open;for(let t,r=n.target;r&&r!=this.dom;r=r.parentNode)if("LI"==r.nodeName&&(t=/-(\d+)$/.exec(r.id))&&+t[1]<i.length)return this.applyCompletion(e,i[+t[1]]),void n.preventDefault()})),this.dom.addEventListener("focusout",(t=>{let n=e.state.field(this.stateField,!1);n&&n.tooltip&&e.state.facet(Ce).closeOnBlur&&t.relatedTarget!=e.contentDOM&&e.dispatch({effects:xe.of(null)})})),this.showOptions(r,i.id)}mount(){this.updateSel()}showOptions(e,t){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(e,t,this.range)),this.list.addEventListener("scroll",(()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)}))}update(e){var t;let n=e.state.field(this.stateField),i=e.startState.field(this.stateField);if(this.updateTooltipClass(e.state),n!=i){let{options:r,selected:o,disabled:s}=n.open;i.open&&i.open.options==r||(this.range=Oe(r.length,o,e.state.facet(Ce).maxRenderedOptions),this.showOptions(r,n.id)),this.updateSel(),s!=(null===(t=i.open)||void 0===t?void 0:t.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!s)}}updateTooltipClass(e){let t=this.tooltipClass(e);if(t!=this.currentClass){for(let e of this.currentClass.split(" "))e&&this.dom.classList.remove(e);for(let e of t.split(" "))e&&this.dom.classList.add(e);this.currentClass=t}}positioned(e){this.space=e,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let e=this.view.state.field(this.stateField),t=e.open;if((t.selected>-1&&t.selected<this.range.from||t.selected>=this.range.to)&&(this.range=Oe(t.options.length,t.selected,this.view.state.facet(Ce).maxRenderedOptions),this.showOptions(t.options,e.id)),this.updateSelectedOption(t.selected)){this.destroyInfo();let{completion:n}=t.options[t.selected],{info:r}=n;if(!r)return;let o="string"===typeof r?document.createTextNode(r):r(n);if(!o)return;"then"in o?o.then((t=>{t&&this.view.state.field(this.stateField,!1)==e&&this.addInfoPane(t,n)})).catch((e=>(0,i.logException)(this.view.state,e,"completion info"))):this.addInfoPane(o,n)}}addInfoPane(e,t){this.destroyInfo();let n=this.info=document.createElement("div");if(n.className="cm-tooltip cm-completionInfo",null!=e.nodeType)n.appendChild(e),this.infoDestroy=null;else{let{dom:t,destroy:i}=e;n.appendChild(t),this.infoDestroy=i||null}this.dom.appendChild(n),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(e){let t=null;for(let n=this.list.firstChild,i=this.range.from;n;n=n.nextSibling,i++)"LI"==n.nodeName&&n.id?i==e?n.hasAttribute("aria-selected")||(n.setAttribute("aria-selected","true"),t=n):n.hasAttribute("aria-selected")&&n.removeAttribute("aria-selected"):i--;return t&&function(e,t){let n=e.getBoundingClientRect(),i=t.getBoundingClientRect(),r=n.height/e.offsetHeight;i.top<n.top?e.scrollTop-=(n.top-i.top)/r:i.bottom>n.bottom&&(e.scrollTop+=(i.bottom-n.bottom)/r)}(this.list,t),t}measureInfo(){let e=this.dom.querySelector("[aria-selected]");if(!e||!this.info)return null;let t=this.dom.getBoundingClientRect(),n=this.info.getBoundingClientRect(),i=e.getBoundingClientRect(),r=this.space;if(!r){let e=this.dom.ownerDocument.documentElement;r={left:0,top:0,right:e.clientWidth,bottom:e.clientHeight}}return i.top>Math.min(r.bottom,t.bottom)-10||i.bottom<Math.max(r.top,t.top)+10?null:this.view.state.facet(Ce).positionInfo(this.view,t,i,n,r,this.dom)}placeInfo(e){this.info&&(e?(e.style&&(this.info.style.cssText=e.style),this.info.className="cm-tooltip cm-completionInfo "+(e.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(e,t,n){const i=document.createElement("ul");i.id=t,i.setAttribute("role","listbox"),i.setAttribute("aria-expanded","true"),i.setAttribute("aria-label",this.view.state.phrase("Completions")),i.addEventListener("mousedown",(e=>{e.target==i&&e.preventDefault()}));let r=null;for(let o=n.from;o<n.to;o++){let{completion:s,match:l}=e[o],{section:a}=s;if(a){let e="string"==typeof a?a:a.name;if(e!=r&&(o>n.from||0==n.from))if(r=e,"string"!=typeof a&&a.header)i.appendChild(a.header(a));else{i.appendChild(document.createElement("completion-section")).textContent=e}}const c=i.appendChild(document.createElement("li"));c.id=t+"-"+o,c.setAttribute("role","option");let h=this.optionClass(s);h&&(c.className=h);for(let e of this.optionContent){let t=e(s,this.view.state,this.view,l);t&&c.appendChild(t)}}return n.from&&i.classList.add("cm-completionListIncompleteTop"),n.to<e.length&&i.classList.add("cm-completionListIncompleteBottom"),i}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function Me(e,t){return n=>new De(n,e,t)}function Pe(e){return 100*(e.boost||0)+(e.apply?10:0)+(e.info?5:0)+(e.type?1:0)}class Te{constructor(e,t,n,i,r,o){this.options=e,this.attrs=t,this.tooltip=n,this.timestamp=i,this.selected=r,this.disabled=o}setSelected(e,t){return e==this.selected||e>=this.options.length?this:new Te(this.options,Re(t,e),this.tooltip,this.timestamp,e,this.disabled)}static build(e,t,n,i,r,o){if(i&&!o&&e.some((e=>e.isPending)))return i.setDisabled();let s=function(e,t){let n=[],i=null,r=e=>{n.push(e);let{section:t}=e.completion;if(t){i||(i=[]);let e="string"==typeof t?t:t.name;i.some((t=>t.name==e))||i.push("string"==typeof t?{name:e}:t)}},o=t.facet(Ce);for(let c of e)if(c.hasResult()){let e=c.result.getMatch;if(!1===c.result.filter)for(let t of c.result.options)r(new pe(t,c.source,e?e(t):[],1e9-n.length));else{let n,i=t.sliceDoc(c.from,c.to),s=o.filterStrict?new Se(i):new ke(i);for(let t of c.result.options)if(n=s.match(t.label)){let i=t.displayLabel?e?e(t,n.matched):[]:n.matched;r(new pe(t,c.source,i,n.score+(t.boost||0)))}}}if(i){let e=Object.create(null),t=0,r=(e,t)=>{var n,i;return(null!==(n=e.rank)&&void 0!==n?n:1e9)-(null!==(i=t.rank)&&void 0!==i?i:1e9)||(e.name<t.name?-1:1)};for(let n of i.sort(r))t-=1e5,e[n.name]=t;for(let i of n){let{section:t}=i.completion;t&&(i.score+=e["string"==typeof t?t:t.name])}}let s=[],l=null,a=o.compareCompletions;for(let c of n.sort(((e,t)=>t.score-e.score||a(e.completion,t.completion)))){let e=c.completion;!l||l.label!=e.label||l.detail!=e.detail||null!=l.type&&null!=e.type&&l.type!=e.type||l.apply!=e.apply||l.boost!=e.boost?s.push(c):Pe(c.completion)>Pe(l)&&(s[s.length-1]=c),l=c.completion}return s}(e,t);if(!s.length)return i&&e.some((e=>e.isPending))?i.setDisabled():null;let l=t.facet(Ce).selectOnOpen?0:-1;if(i&&i.selected!=l&&-1!=i.selected){let e=i.options[i.selected].completion;for(let t=0;t<s.length;t++)if(s[t].completion==e){l=t;break}}return new Te(s,Re(n,l),{pos:e.reduce(((e,t)=>t.hasResult()?Math.min(e,t.from):e),1e8),create:Ue,above:r.aboveCursor},i?i.timestamp:Date.now(),l,!1)}map(e){return new Te(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:e.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new Te(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class Ie{constructor(e,t,n){this.active=e,this.id=t,this.open=n}static start(){return new Ie(Le,"cm-ac-"+Math.floor(2e6*Math.random()).toString(36),null)}update(e){let{state:t}=e,n=t.facet(Ce),i=(n.override||t.languageDataAt("autocomplete",me(t)).map(be)).map((t=>(this.active.find((e=>e.source==t))||new Fe(t,this.active.some((e=>0!=e.state))?1:0)).update(e,n)));i.length==this.active.length&&i.every(((e,t)=>e==this.active[t]))&&(i=this.active);let r=this.open,o=e.effects.some((e=>e.is(Ve)));r&&e.docChanged&&(r=r.map(e.changes)),e.selection||i.some((t=>t.hasResult()&&e.changes.touchesRange(t.from,t.to)))||!function(e,t){if(e==t)return!0;for(let n=0,i=0;;){for(;n<e.length&&!e[n].hasResult();)n++;for(;i<t.length&&!t[i].hasResult();)i++;let r=n==e.length,o=i==t.length;if(r||o)return r==o;if(e[n++].result!=t[i++].result)return!1}}(i,this.active)||o?r=Te.build(i,t,this.id,r,n,o):r&&r.disabled&&!i.some((e=>e.isPending))&&(r=null),!r&&i.every((e=>!e.isPending))&&i.some((e=>e.hasResult()))&&(i=i.map((e=>e.hasResult()?new Fe(e.source,0):e)));for(let s of e.effects)s.is(qe)&&(r=r&&r.setSelected(s.value,this.id));return i==this.active&&r==this.open?this:new Ie(i,this.id,r)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?Be:Ne}}const Be={"aria-autocomplete":"list"},Ne={};function Re(e,t){let n={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":e};return t>-1&&(n["aria-activedescendant"]=e+"-"+t),n}const Le=[];function je(e,t){if(e.isUserEvent("input.complete")){let n=e.annotation(ye);if(n&&t.activateOnCompletion(n))return 12}let n=e.isUserEvent("input.type");return n&&t.activateOnTyping?5:n?1:e.isUserEvent("delete.backward")?2:e.selection?8:e.docChanged?16:0}class Fe{constructor(e,t,n=!1){this.source=e,this.state=t,this.explicit=n}hasResult(){return!1}get isPending(){return 1==this.state}update(e,t){let n=je(e,t),i=this;(8&n||16&n&&this.touches(e))&&(i=new Fe(i.source,0)),4&n&&0==i.state&&(i=new Fe(this.source,1)),i=i.updateFor(e,n);for(let r of e.effects)if(r.is(we))i=new Fe(i.source,1,r.value);else if(r.is(xe))i=new Fe(i.source,0);else if(r.is(Ve))for(let e of r.value)e.source==i.source&&(i=e);return i}updateFor(e,t){return this.map(e.changes)}map(e){return this}touches(e){return e.changes.touchesRange(me(e.state))}}class We extends Fe{constructor(e,t,n,i,r,o){super(e,3,t),this.limit=n,this.result=i,this.from=r,this.to=o}hasResult(){return!0}updateFor(e,t){var n;if(!(3&t))return this.map(e.changes);let i=this.result;i.map&&!e.changes.empty&&(i=i.map(i,e.changes));let r=e.changes.mapPos(this.from),o=e.changes.mapPos(this.to,1),s=me(e.state);if(s>o||!i||2&t&&(me(e.startState)==this.from||s<this.limit))return new Fe(this.source,4&t?1:0);let l=e.changes.mapPos(this.limit);return function(e,t,n,i){if(!e)return!1;let r=t.sliceDoc(n,i);return"function"==typeof e?e(r,n,i,t):ge(e,!0).test(r)}(i.validFor,e.state,r,o)?new We(this.source,this.explicit,l,i,r,o):i.update&&(i=i.update(i,r,o,new ue(e.state,s,!1)))?new We(this.source,this.explicit,l,i,i.from,null!==(n=i.to)&&void 0!==n?n:me(e.state)):new Fe(this.source,1,this.explicit)}map(e){if(e.empty)return this;return(this.result.map?this.result.map(this.result,e):this.result)?new We(this.source,this.explicit,e.mapPos(this.limit),this.result,e.mapPos(this.from),e.mapPos(this.to,1)):new Fe(this.source,0)}touches(e){return e.changes.touchesRange(this.from,this.to)}}const Ve=r.StateEffect.define({map:(e,t)=>e.map((e=>e.map(t)))}),qe=r.StateEffect.define(),_e=r.StateField.define({create:()=>Ie.start(),update:(e,t)=>e.update(t),provide:e=>[i.showTooltip.from(e,(e=>e.tooltip)),i.EditorView.contentAttributes.from(e,(e=>e.attrs))]});function ze(e,t){const n=t.completion.apply||t.completion.label;let i=e.state.field(_e).active.find((e=>e.source==t.source));return i instanceof We&&("string"==typeof n?e.dispatch(Object.assign(Object.assign({},function(e,t,n,i){let{main:o}=e.selection,s=n-o.from,l=i-o.from;return Object.assign(Object.assign({},e.changeByRange((a=>{if(a!=o&&n!=i&&e.sliceDoc(a.from+s,a.from+l)!=e.sliceDoc(n,i))return{range:a};let c=e.toText(t);return{changes:{from:a.from+s,to:i==o.from?a.to:a.from+l,insert:c},range:r.EditorSelection.cursor(a.from+s+c.length)}}))),{scrollIntoView:!0,userEvent:"input.complete"})}(e.state,n,i.from,i.to)),{annotations:ye.of(t.completion)})):n(e,t.completion,i.from,i.to),!0)}const Ue=Me(_e,ze);function He(e,t="option"){return n=>{let r=n.state.field(_e,!1);if(!r||!r.open||r.open.disabled||Date.now()-r.open.timestamp<n.state.facet(Ce).interactionDelay)return!1;let o,s=1;"page"==t&&(o=(0,i.getTooltip)(n,r.open.tooltip))&&(s=Math.max(2,Math.floor(o.dom.offsetHeight/o.dom.querySelector("li").offsetHeight)-1));let{length:l}=r.open.options,a=r.open.selected>-1?r.open.selected+s*(e?1:-1):e?0:l-1;return a<0?a="page"==t?0:l-1:a>=l&&(a="page"==t?l-1:0),n.dispatch({effects:qe.of(a)}),!0}}const $e=e=>!!e.state.field(_e,!1)&&(e.dispatch({effects:we.of(!0)}),!0);class Je{constructor(e,t){this.active=e,this.context=t,this.time=Date.now(),this.updates=[],this.done=void 0}}const Ke=i.ViewPlugin.fromClass(class{constructor(e){this.view=e,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0;for(let t of e.state.field(_e).active)t.isPending&&this.startQuery(t)}update(e){let t=e.state.field(_e),n=e.state.facet(Ce);if(!e.selectionSet&&!e.docChanged&&e.startState.field(_e)==t)return;let r=e.transactions.some((e=>{let t=je(e,n);return 8&t||(e.selection||e.docChanged)&&!(3&t)}));for(let l=0;l<this.running.length;l++){let t=this.running[l];if(r||t.context.abortOnDocChange&&e.docChanged||t.updates.length+e.transactions.length>50&&Date.now()-t.time>1e3){for(let e of t.context.abortListeners)try{e()}catch(s){(0,i.logException)(this.view.state,s)}t.context.abortListeners=null,this.running.splice(l--,1)}else t.updates.push(...e.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),e.transactions.some((e=>e.effects.some((e=>e.is(we)))))&&(this.pendingStart=!0);let o=this.pendingStart?50:n.activateOnTypingDelay;if(this.debounceUpdate=t.active.some((e=>e.isPending&&!this.running.some((t=>t.active.source==e.source))))?setTimeout((()=>this.startUpdate()),o):-1,0!=this.composing)for(let i of e.transactions)i.isUserEvent("input.type")?this.composing=2:2==this.composing&&i.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:e}=this.view,t=e.field(_e);for(let n of t.active)n.isPending&&!this.running.some((e=>e.active.source==n.source))&&this.startQuery(n);this.running.length&&t.open&&t.open.disabled&&(this.debounceAccept=setTimeout((()=>this.accept()),this.view.state.facet(Ce).updateSyncTime))}startQuery(e){let{state:t}=this.view,n=me(t),r=new ue(t,n,e.explicit,this.view),o=new Je(e,r);this.running.push(o),Promise.resolve(e.source(r)).then((e=>{o.context.aborted||(o.done=e||null,this.scheduleAccept())}),(e=>{this.view.dispatch({effects:xe.of(null)}),(0,i.logException)(this.view.state,e)}))}scheduleAccept(){this.running.every((e=>void 0!==e.done))?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout((()=>this.accept()),this.view.state.facet(Ce).updateSyncTime))}accept(){var e;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let t=[],n=this.view.state.facet(Ce),i=this.view.state.field(_e);for(let r=0;r<this.running.length;r++){let o=this.running[r];if(void 0===o.done)continue;if(this.running.splice(r--,1),o.done){let i=me(o.updates.length?o.updates[0].startState:this.view.state),r=Math.min(i,o.done.from+(o.active.explicit?0:1)),s=new We(o.active.source,o.active.explicit,r,o.done,o.done.from,null!==(e=o.done.to)&&void 0!==e?e:i);for(let e of o.updates)s=s.update(e,n);if(s.hasResult()){t.push(s);continue}}let s=i.active.find((e=>e.source==o.active.source));if(s&&s.isPending)if(null==o.done){let e=new Fe(o.active.source,0);for(let t of o.updates)e=e.update(t,n);e.isPending||t.push(e)}else this.startQuery(s)}(t.length||i.open&&i.open.disabled)&&this.view.dispatch({effects:Ve.of(t)})}},{eventHandlers:{blur(e){let t=this.view.state.field(_e,!1);if(t&&t.tooltip&&this.view.state.facet(Ce).closeOnBlur){let n=t.open&&(0,i.getTooltip)(this.view,t.open.tooltip);n&&n.dom.contains(e.relatedTarget)||setTimeout((()=>this.view.dispatch({effects:xe.of(null)})),10)}},compositionstart(){this.composing=1},compositionend(){3==this.composing&&setTimeout((()=>this.view.dispatch({effects:we.of(!1)})),20),this.composing=0}}}),Ye="object"==typeof navigator&&/Win/.test(navigator.platform),Ge=r.Prec.highest(i.EditorView.domEventHandlers({keydown(e,t){let n=t.state.field(_e,!1);if(!n||!n.open||n.open.disabled||n.open.selected<0||e.key.length>1||e.ctrlKey&&(!Ye||!e.altKey)||e.metaKey)return!1;let i=n.open.options[n.open.selected],r=n.active.find((e=>e.source==i.source)),o=i.completion.commitCharacters||r.result.commitCharacters;return o&&o.indexOf(e.key)>-1&&ze(t,i),!1}})),Ze=i.EditorView.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"\xb7\xb7\xb7"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'\u0192'"}},".cm-completionIcon-class":{"&:after":{content:"'\u25cb'"}},".cm-completionIcon-interface":{"&:after":{content:"'\u25cc'"}},".cm-completionIcon-variable":{"&:after":{content:"'\ud835\udc65'"}},".cm-completionIcon-constant":{"&:after":{content:"'\ud835\udc36'"}},".cm-completionIcon-type":{"&:after":{content:"'\ud835\udc61'"}},".cm-completionIcon-enum":{"&:after":{content:"'\u222a'"}},".cm-completionIcon-property":{"&:after":{content:"'\u25a1'"}},".cm-completionIcon-keyword":{"&:after":{content:"'\ud83d\udd11\ufe0e'"}},".cm-completionIcon-namespace":{"&:after":{content:"'\u25a2'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});const Qe={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},Xe=r.StateEffect.define({map(e,t){let n=t.mapPos(e,-1,r.MapMode.TrackAfter);return null==n?void 0:n}}),et=new class extends r.RangeValue{};et.startSide=1,et.endSide=-1;const tt=r.StateField.define({create:()=>r.RangeSet.empty,update(e,t){if(e=e.map(t.changes),t.selection){let n=t.state.doc.lineAt(t.selection.main.head);e=e.update({filter:e=>e>=n.from&&e<=n.to})}for(let n of t.effects)n.is(Xe)&&(e=e.update({add:[et.range(n.value,n.value+1)]}));return e}});const nt="()[]{}<>\xab\xbb\xbb\xab\uff3b\uff3d\uff5b\uff5d";function it(e){for(let t=0;t<16;t+=2)if(nt.charCodeAt(t)==e)return nt.charAt(t+1);return(0,r.fromCodePoint)(e<128?e:e+1)}function rt(e,t){return e.languageDataAt("closeBrackets",t)[0]||Qe}const ot="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),st=i.EditorView.inputHandler.of(((e,t,n,i)=>{if((ot?e.composing:e.compositionStarted)||e.state.readOnly)return!1;let o=e.state.selection.main;if(i.length>2||2==i.length&&1==(0,r.codePointSize)((0,r.codePointAt)(i,0))||t!=o.from||n!=o.to)return!1;let s=function(e,t){let n=rt(e,e.selection.main.head),i=n.brackets||Qe.brackets;for(let o of i){let s=it((0,r.codePointAt)(o,0));if(t==o)return s==o?ft(e,o,i.indexOf(o+o+o)>-1,n):ht(e,o,s,n.before||Qe.before);if(t==s&&at(e,e.selection.main.from))return ut(e,o,s)}return null}(e.state,i);return!!s&&(e.dispatch(s),!0)})),lt=[{key:"Backspace",run:({state:e,dispatch:t})=>{if(e.readOnly)return!1;let n=rt(e,e.selection.main.head).brackets||Qe.brackets,i=null,o=e.changeByRange((t=>{if(t.empty){let i=function(e,t){let n=e.sliceString(t-2,t);return(0,r.codePointSize)((0,r.codePointAt)(n,0))==n.length?n:n.slice(1)}(e.doc,t.head);for(let o of n)if(o==i&&ct(e.doc,t.head)==it((0,r.codePointAt)(o,0)))return{changes:{from:t.head-o.length,to:t.head+o.length},range:r.EditorSelection.cursor(t.head-o.length)}}return{range:i=t}}));return i||t(e.update(o,{scrollIntoView:!0,userEvent:"delete.backward"})),!i}}];function at(e,t){let n=!1;return e.field(tt).between(0,e.doc.length,(e=>{e==t&&(n=!0)})),n}function ct(e,t){let n=e.sliceString(t,t+2);return n.slice(0,(0,r.codePointSize)((0,r.codePointAt)(n,0)))}function ht(e,t,n,i){let o=null,s=e.changeByRange((s=>{if(!s.empty)return{changes:[{insert:t,from:s.from},{insert:n,from:s.to}],effects:Xe.of(s.to+t.length),range:r.EditorSelection.range(s.anchor+t.length,s.head+t.length)};let l=ct(e.doc,s.head);return!l||/\s/.test(l)||i.indexOf(l)>-1?{changes:{insert:t+n,from:s.head},effects:Xe.of(s.head+t.length),range:r.EditorSelection.cursor(s.head+t.length)}:{range:o=s}}));return o?null:e.update(s,{scrollIntoView:!0,userEvent:"input.type"})}function ut(e,t,n){let i=null,o=e.changeByRange((t=>t.empty&&ct(e.doc,t.head)==n?{changes:{from:t.head,to:t.head+n.length,insert:n},range:r.EditorSelection.cursor(t.head+n.length)}:i={range:t}));return i?null:e.update(o,{scrollIntoView:!0,userEvent:"input.type"})}function ft(e,t,n,i){let o=i.stringPrefixes||Qe.stringPrefixes,s=null,l=e.changeByRange((i=>{if(!i.empty)return{changes:[{insert:t,from:i.from},{insert:t,from:i.to}],effects:Xe.of(i.to+t.length),range:r.EditorSelection.range(i.anchor+t.length,i.head+t.length)};let l,a=i.head,c=ct(e.doc,a);if(c==t){if(dt(e,a))return{changes:{insert:t+t,from:a},effects:Xe.of(a+t.length),range:r.EditorSelection.cursor(a+t.length)};if(at(e,a)){let i=n&&e.sliceDoc(a,a+3*t.length)==t+t+t?t+t+t:t;return{changes:{from:a,to:a+i.length,insert:i},range:r.EditorSelection.cursor(a+i.length)}}}else{if(n&&e.sliceDoc(a-2*t.length,a)==t+t&&(l=pt(e,a-2*t.length,o))>-1&&dt(e,l))return{changes:{insert:t+t+t+t,from:a},effects:Xe.of(a+t.length),range:r.EditorSelection.cursor(a+t.length)};if(e.charCategorizer(a)(c)!=r.CharCategory.Word&&pt(e,a,o)>-1&&!function(e,t,n,i){let r=(0,he.mv)(e).resolveInner(t,-1),o=i.reduce(((e,t)=>Math.max(e,t.length)),0);for(let s=0;s<5;s++){let s=e.sliceDoc(r.from,Math.min(r.to,r.from+n.length+o)),l=s.indexOf(n);if(!l||l>-1&&i.indexOf(s.slice(0,l))>-1){let t=r.firstChild;for(;t&&t.from==r.from&&t.to-t.from>n.length+l;){if(e.sliceDoc(t.to-n.length,t.to)==n)return!1;t=t.firstChild}return!0}let a=r.to==t&&r.parent;if(!a)break;r=a}return!1}(e,a,t,o))return{changes:{insert:t+t,from:a},effects:Xe.of(a+t.length),range:r.EditorSelection.cursor(a+t.length)}}return{range:s=i}}));return s?null:e.update(l,{scrollIntoView:!0,userEvent:"input.type"})}function dt(e,t){let n=(0,he.mv)(e).resolveInner(t+1);return n.parent&&n.from==t}function pt(e,t,n){let i=e.charCategorizer(t);if(i(e.sliceDoc(t-1,t))!=r.CharCategory.Word)return t;for(let o of n){let n=t-o.length;if(e.sliceDoc(n,t)==o&&i(e.sliceDoc(n-1,n))!=r.CharCategory.Word)return n}return-1}const mt=[{key:"Ctrl-Space",run:$e},{mac:"Alt-`",run:$e},{key:"Escape",run:e=>{let t=e.state.field(_e,!1);return!(!t||!t.active.some((e=>0!=e.state)))&&(e.dispatch({effects:xe.of(null)}),!0)}},{key:"ArrowDown",run:He(!0)},{key:"ArrowUp",run:He(!1)},{key:"PageDown",run:He(!0,"page")},{key:"PageUp",run:He(!1,"page")},{key:"Enter",run:e=>{let t=e.state.field(_e,!1);return!(e.state.readOnly||!t||!t.open||t.open.selected<0||t.open.disabled||Date.now()-t.open.timestamp<e.state.facet(Ce).interactionDelay)&&ze(e,t.open.options[t.open.selected])}}],gt=r.Prec.highest(i.keymap.computeN([Ce],(e=>e.facet(Ce).defaultKeymap?[mt]:[])));class yt{constructor(e,t,n){this.from=e,this.to=t,this.diagnostic=n}}class vt{constructor(e,t,n){this.diagnostics=e,this.panel=t,this.selected=n}static init(e,t,n){let o=n.facet(Tt).markerFilter;o&&(e=o(e,n));let s=e.slice().sort(((e,t)=>e.from-t.from||e.to-t.to)),l=new r.RangeSetBuilder,a=[],c=0;for(let r=0;;){let e,t,o=r==s.length?null:s[r];if(!o&&!a.length)break;for(a.length?(e=c,t=a.reduce(((e,t)=>Math.min(e,t.to)),o&&o.from>e?o.from:1e8)):(e=o.from,t=o.to,a.push(o),r++);r<s.length;){let n=s[r];if(n.from!=e||!(n.to>n.from||n.to==e)){t=Math.min(n.from,t);break}a.push(n),r++,t=Math.min(n.to,t)}let h=qt(a);if(a.some((e=>e.from==e.to||e.from==e.to-1&&n.doc.lineAt(e.from).to==e.from)))l.add(e,e,i.Decoration.widget({widget:new Nt(h),diagnostics:a.slice()}));else{let n=a.reduce(((e,t)=>t.markClass?e+" "+t.markClass:e),"");l.add(e,t,i.Decoration.mark({class:"cm-lintRange cm-lintRange-"+h+n,diagnostics:a.slice(),inclusiveEnd:a.some((e=>e.to>t))}))}c=t;for(let n=0;n<a.length;n++)a[n].to<=c&&a.splice(n--,1)}let h=l.finish();return new vt(h,t,bt(h))}}function bt(e,t=null,n=0){let i=null;return e.between(n,1e9,((e,n,{spec:r})=>{if(!(t&&r.diagnostics.indexOf(t)<0))if(i){if(r.diagnostics.indexOf(i.diagnostic)<0)return!1;i=new yt(i.from,n,i.diagnostic)}else i=new yt(e,n,t||r.diagnostics[0])})),i}function wt(e,t){let n=t.pos,i=t.end||n,r=e.state.facet(Tt).hideOn(e,n,i);if(null!=r)return r;let o=e.startState.doc.lineAt(t.pos);return!(!e.effects.some((e=>e.is(kt)))&&!e.changes.touchesRange(o.from,Math.max(o.to,i)))}function xt(e,t){return e.field(At,!1)?t:t.concat(r.StateEffect.appendConfig.of(_t))}const kt=r.StateEffect.define(),St=r.StateEffect.define(),Ct=r.StateEffect.define(),At=r.StateField.define({create:()=>new vt(i.Decoration.none,null,null),update(e,t){if(t.docChanged&&e.diagnostics.size){let n=e.diagnostics.map(t.changes),i=null,r=e.panel;if(e.selected){let r=t.changes.mapPos(e.selected.from,1);i=bt(n,e.selected.diagnostic,r)||bt(n,null,r)}!n.size&&r&&t.state.facet(Tt).autoPanel&&(r=null),e=new vt(n,r,i)}for(let n of t.effects)if(n.is(kt)){let i=t.state.facet(Tt).autoPanel?n.value.length?Lt.open:null:e.panel;e=vt.init(n.value,i,t.state)}else n.is(St)?e=new vt(e.diagnostics,n.value?Lt.open:null,e.selected):n.is(Ct)&&(e=new vt(e.diagnostics,e.panel,n.value));return e},provide:e=>[i.showPanel.from(e,(e=>e.panel)),i.EditorView.decorations.from(e,(e=>e.diagnostics))]});const Et=i.Decoration.mark({class:"cm-lintRange cm-lintRange-active"});function Ot(e,t,n){let i,{diagnostics:r}=e.state.field(At),o=-1,s=-1;r.between(t-(n<0?1:0),t+(n>0?1:0),((e,r,{spec:l})=>{if(t>=e&&t<=r&&(e==r||(t>e||n>0)&&(t<r||n<0)))return i=l.diagnostics,o=e,s=r,!1}));let l=e.state.facet(Tt).tooltipFilter;return i&&l&&(i=l(i,e.state)),i?{pos:o,end:s,above:e.state.doc.lineAt(o).to<s,create:()=>({dom:Dt(e,i)})}:null}function Dt(e,t){return s("ul",{class:"cm-tooltip-lint"},t.map((t=>Bt(e,t,!1))))}const Mt=e=>{let t=e.state.field(At,!1);return!(!t||!t.panel)&&(e.dispatch({effects:St.of(!1)}),!0)},Pt=[{key:"Mod-Shift-m",run:e=>{let t=e.state.field(At,!1);t&&t.panel||e.dispatch({effects:xt(e.state,[St.of(!0)])});let n=(0,i.getPanel)(e,Lt.open);return n&&n.dom.querySelector(".cm-panel-lint ul").focus(),!0},preventDefault:!0},{key:"F8",run:e=>{let t=e.state.field(At,!1);if(!t)return!1;let n=e.state.selection.main,i=t.diagnostics.iter(n.to+1);return!(!i.value&&(i=t.diagnostics.iter(0),!i.value||i.from==n.from&&i.to==n.to))&&(e.dispatch({selection:{anchor:i.from,head:i.to},scrollIntoView:!0}),!0)}}];const Tt=r.Facet.define({combine:e=>Object.assign({sources:e.map((e=>e.source)).filter((e=>null!=e))},(0,r.combineConfig)(e.map((e=>e.config)),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null,hideOn:()=>null},{needsRefresh:(e,t)=>e?t?n=>e(n)||t(n):e:t}))});function It(e){let t=[];if(e)e:for(let{name:n}of e){for(let e=0;e<n.length;e++){let i=n[e];if(/[a-zA-Z]/.test(i)&&!t.some((e=>e.toLowerCase()==i.toLowerCase()))){t.push(i);continue e}}t.push("")}return t}function Bt(e,t,n){var i;let r=n?It(t.actions):[];return s("li",{class:"cm-diagnostic cm-diagnostic-"+t.severity},s("span",{class:"cm-diagnosticText"},t.renderMessage?t.renderMessage(e):t.message),null===(i=t.actions)||void 0===i?void 0:i.map(((n,i)=>{let o=!1,l=i=>{if(i.preventDefault(),o)return;o=!0;let r=bt(e.state.field(At).diagnostics,t);r&&n.apply(e,r.from,r.to)},{name:a}=n,c=r[i]?a.indexOf(r[i]):-1,h=c<0?a:[a.slice(0,c),s("u",a.slice(c,c+1)),a.slice(c+1)];return s("button",{type:"button",class:"cm-diagnosticAction",onclick:l,onmousedown:l,"aria-label":` Action: ${a}${c<0?"":` (access key "${r[i]})"`}.`},h)})),t.source&&s("div",{class:"cm-diagnosticSource"},t.source))}class Nt extends i.WidgetType{constructor(e){super(),this.sev=e}eq(e){return e.sev==this.sev}toDOM(){return s("span",{class:"cm-lintPoint cm-lintPoint-"+this.sev})}}class Rt{constructor(e,t){this.diagnostic=t,this.id="item_"+Math.floor(4294967295*Math.random()).toString(16),this.dom=Bt(e,t,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class Lt{constructor(e){this.view=e,this.items=[];this.list=s("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:t=>{if(27==t.keyCode)Mt(this.view),this.view.focus();else if(38==t.keyCode||33==t.keyCode)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(40==t.keyCode||34==t.keyCode)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(36==t.keyCode)this.moveSelection(0);else if(35==t.keyCode)this.moveSelection(this.items.length-1);else if(13==t.keyCode)this.view.focus();else{if(!(t.keyCode>=65&&t.keyCode<=90&&this.selectedIndex>=0))return;{let{diagnostic:n}=this.items[this.selectedIndex],i=It(n.actions);for(let r=0;r<i.length;r++)if(i[r].toUpperCase().charCodeAt(0)==t.keyCode){let t=bt(this.view.state.field(At).diagnostics,n);t&&n.actions[r].apply(e,t.from,t.to)}}}t.preventDefault()},onclick:e=>{for(let t=0;t<this.items.length;t++)this.items[t].dom.contains(e.target)&&this.moveSelection(t)}}),this.dom=s("div",{class:"cm-panel-lint"},this.list,s("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>Mt(this.view)},"\xd7")),this.update()}get selectedIndex(){let e=this.view.state.field(At).selected;if(!e)return-1;for(let t=0;t<this.items.length;t++)if(this.items[t].diagnostic==e.diagnostic)return t;return-1}update(){let{diagnostics:e,selected:t}=this.view.state.field(At),n=0,i=!1,r=null,o=new Set;for(e.between(0,this.view.state.doc.length,((e,s,{spec:l})=>{for(let a of l.diagnostics){if(o.has(a))continue;o.add(a);let e,s=-1;for(let t=n;t<this.items.length;t++)if(this.items[t].diagnostic==a){s=t;break}s<0?(e=new Rt(this.view,a),this.items.splice(n,0,e),i=!0):(e=this.items[s],s>n&&(this.items.splice(n,s-n),i=!0)),t&&e.diagnostic==t.diagnostic?e.dom.hasAttribute("aria-selected")||(e.dom.setAttribute("aria-selected","true"),r=e):e.dom.hasAttribute("aria-selected")&&e.dom.removeAttribute("aria-selected"),n++}}));n<this.items.length&&!(1==this.items.length&&this.items[0].diagnostic.from<0);)i=!0,this.items.pop();0==this.items.length&&(this.items.push(new Rt(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),i=!0),r?(this.list.setAttribute("aria-activedescendant",r.id),this.view.requestMeasure({key:this,read:()=>({sel:r.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:e,panel:t})=>{let n=t.height/this.list.offsetHeight;e.top<t.top?this.list.scrollTop-=(t.top-e.top)/n:e.bottom>t.bottom&&(this.list.scrollTop+=(e.bottom-t.bottom)/n)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),i&&this.sync()}sync(){let e=this.list.firstChild;function t(){let t=e;e=t.nextSibling,t.remove()}for(let n of this.items)if(n.dom.parentNode==this.list){for(;e!=n.dom;)t();e=n.dom.nextSibling}else this.list.insertBefore(n.dom,e);for(;e;)t()}moveSelection(e){if(this.selectedIndex<0)return;let t=bt(this.view.state.field(At).diagnostics,this.items[e].diagnostic);t&&this.view.dispatch({selection:{anchor:t.from,head:t.to},scrollIntoView:!0,effects:Ct.of(t)})}static open(e){return new Lt(e)}}function jt(e,t='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${t}>${encodeURIComponent(e)}</svg>')`}function Ft(e){return jt(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${e}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}const Wt=i.EditorView.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:Ft("#d11")},".cm-lintRange-warning":{backgroundImage:Ft("orange")},".cm-lintRange-info":{backgroundImage:Ft("#999")},".cm-lintRange-hint":{backgroundImage:Ft("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});function Vt(e){return"error"==e?4:"warning"==e?3:"info"==e?2:1}function qt(e){let t="hint",n=1;for(let i of e){let e=Vt(i.severity);e>n&&(n=e,t=i.severity)}return t}i.GutterMarker;const _t=[At,i.EditorView.decorations.compute([At],(e=>{let{selected:t,panel:n}=e.field(At);return t&&n&&t.from!=t.to?i.Decoration.set([Et.range(t.from,t.to)]):i.Decoration.none})),(0,i.hoverTooltip)(Ot,{hideOn:wt}),Wt];var zt=function(e){void 0===e&&(e={});var{crosshairCursor:t=!1}=e,n=[];!1!==e.closeBracketsKeymap&&(n=n.concat(lt)),!1!==e.defaultKeymap&&(n=n.concat(o.pw)),!1!==e.searchKeymap&&(n=n.concat(ne)),!1!==e.historyKeymap&&(n=n.concat(o.cL)),!1!==e.foldKeymap&&(n=n.concat(he.f7)),!1!==e.completionKeymap&&(n=n.concat(mt)),!1!==e.lintKeymap&&(n=n.concat(Pt));var s=[];return!1!==e.lineNumbers&&s.push((0,i.lineNumbers)()),!1!==e.highlightActiveLineGutter&&s.push((0,i.highlightActiveLineGutter)()),!1!==e.highlightSpecialChars&&s.push((0,i.highlightSpecialChars)()),!1!==e.history&&s.push((0,o.b6)()),!1!==e.foldGutter&&s.push((0,he.Lv)()),!1!==e.drawSelection&&s.push((0,i.drawSelection)()),!1!==e.dropCursor&&s.push((0,i.dropCursor)()),!1!==e.allowMultipleSelections&&s.push(r.EditorState.allowMultipleSelections.of(!0)),!1!==e.indentOnInput&&s.push((0,he.WD)()),!1!==e.syntaxHighlighting&&s.push((0,he.y9)(he.Zt,{fallback:!0})),!1!==e.bracketMatching&&s.push((0,he.SG)()),!1!==e.closeBrackets&&s.push([st,tt]),!1!==e.autocompletion&&s.push(function(e={}){return[Ge,_e,Ce.of(e),Ke,gt,Ze]}()),!1!==e.rectangularSelection&&s.push((0,i.rectangularSelection)()),!1!==t&&s.push((0,i.crosshairCursor)()),!1!==e.highlightActiveLine&&s.push((0,i.highlightActiveLine)()),!1!==e.highlightSelectionMatches&&s.push(function(e){let t=[O,E];return e&&t.push(k.of(e)),t}()),e.tabSize&&"number"===typeof e.tabSize&&s.push(he.Xt.of(" ".repeat(e.tabSize))),s.concat([i.keymap.of(n.flat())]).filter(Boolean)},Ut=function(e){void 0===e&&(e={});var t=[];!1!==e.defaultKeymap&&(t=t.concat(o.pw)),!1!==e.historyKeymap&&(t=t.concat(o.cL));var n=[];return!1!==e.highlightSpecialChars&&n.push((0,i.highlightSpecialChars)()),!1!==e.history&&n.push((0,o.b6)()),!1!==e.drawSelection&&n.push((0,i.drawSelection)()),!1!==e.syntaxHighlighting&&n.push((0,he.y9)(he.Zt,{fallback:!0})),n.concat([i.keymap.of(t.flat())]).filter(Boolean)}},695:(e,t,n)=>{function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function r(e,t){if(e){if("string"==typeof e)return i(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}function o(e){return function(e){if(Array.isArray(e))return i(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||r(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,o,s,l=[],a=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;a=!1}else for(;!(a=(i=o.call(n)).done)&&(l.push(i.value),l.length!==t);a=!0);}catch(e){c=!0,r=e}finally{try{if(!a&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw r}}return l}}(e,t)||r(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.d(t,{q:()=>p});var l=n(442),a=n(60),c=n(730),h=n(89),u=n(369),f=a.Annotation.define(),d=[];function p(e){var t=e.value,n=e.selection,i=e.onChange,r=e.onStatistics,p=e.onCreateEditor,m=e.onUpdate,g=e.extensions,y=void 0===g?d:g,v=e.autoFocus,b=e.theme,w=void 0===b?"light":b,x=e.height,k=void 0===x?null:x,S=e.minHeight,C=void 0===S?null:S,A=e.maxHeight,E=void 0===A?null:A,O=e.width,D=void 0===O?null:O,M=e.minWidth,P=void 0===M?null:M,T=e.maxWidth,I=void 0===T?null:T,B=e.placeholder,N=void 0===B?"":B,R=e.editable,L=void 0===R||R,j=e.readOnly,F=void 0!==j&&j,W=e.indentWithTab,V=void 0===W||W,q=e.basicSetup,_=void 0===q||q,z=e.root,U=e.initialState,H=s((0,l.useState)(),2),$=H[0],J=H[1],K=s((0,l.useState)(),2),Y=K[0],G=K[1],Z=s((0,l.useState)(),2),Q=Z[0],X=Z[1],ee=c.EditorView.theme({"&":{height:k,minHeight:C,maxHeight:E,width:D,minWidth:P,maxWidth:I},"& .cm-scroller":{height:"100% !important"}}),te=c.EditorView.updateListener.of((function(e){if(e.docChanged&&"function"===typeof i&&!e.transactions.some((function(e){return e.annotation(f)}))){var t=e.state.doc.toString();i(t,e)}r&&r((0,u.m)(e))})),ne=(0,h.getDefaultExtensions)({theme:w,editable:L,readOnly:F,placeholder:N,indentWithTab:V,basicSetup:_}),ie=[te,ee].concat(o(ne));return m&&"function"===typeof m&&ie.push(c.EditorView.updateListener.of(m)),ie=ie.concat(y),(0,l.useLayoutEffect)((function(){if($&&!Q){var e={doc:t,selection:n,extensions:ie},i=U?a.EditorState.fromJSON(U.json,e,U.fields):a.EditorState.create(e);if(X(i),!Y){var r=new c.EditorView({state:i,parent:$,root:z});G(r),p&&p(r,i)}}return function(){Y&&(X(void 0),G(void 0))}}),[$,Q]),(0,l.useEffect)((function(){e.container&&J(e.container)}),[e.container]),(0,l.useEffect)((function(){return function(){Y&&(Y.destroy(),G(void 0))}}),[Y]),(0,l.useEffect)((function(){v&&Y&&Y.focus()}),[v,Y]),(0,l.useEffect)((function(){Y&&Y.dispatch({effects:a.StateEffect.reconfigure.of(ie)})}),[w,y,k,C,E,D,P,I,N,L,F,V,_,i,m]),(0,l.useEffect)((function(){if(void 0!==t){var e=Y?Y.state.doc.toString():"";Y&&t!==e&&Y.dispatch({changes:{from:0,to:e.length,insert:t||""},annotations:[f.of(!0)]})}}),[t,Y]),{state:Q,setState:X,view:Y,setView:G,container:$,setContainer:J}}},708:e=>{e.exports=i},720:(e,t,n)=>{n.d(t,{Yc:()=>Re,b6:()=>w,cL:()=>j,pw:()=>Ne});var i=n(60),r=n(730),o=n(194),s=n(203);function l(e,t){return({state:n,dispatch:i})=>{if(n.readOnly)return!1;let r=e(t,n);return!!r&&(i(n.update(r)),!0)}}const a=l(p,0),c=l(d,0),h=l(((e,t)=>d(e,t,function(e){let t=[];for(let n of e.selection.ranges){let i=e.doc.lineAt(n.from),r=n.to<=i.to?i:e.doc.lineAt(n.to);r.from>i.from&&r.from==n.to&&(r=n.to==i.to+1?i:e.doc.lineAt(n.to-1));let o=t.length-1;o>=0&&t[o].to>i.from?t[o].to=r.to:t.push({from:i.from+/^\s*/.exec(i.text)[0].length,to:r.to})}return t}(t))),0);function u(e,t){let n=e.languageDataAt("commentTokens",t,1);return n.length?n[0]:{}}const f=50;function d(e,t,n=t.selection.ranges){let i=n.map((e=>u(t,e.from).block));if(!i.every((e=>e)))return null;let r=n.map(((e,n)=>function(e,{open:t,close:n},i,r){let o,s,l=e.sliceDoc(i-f,i),a=e.sliceDoc(r,r+f),c=/\s*$/.exec(l)[0].length,h=/^\s*/.exec(a)[0].length,u=l.length-c;if(l.slice(u-t.length,u)==t&&a.slice(h,h+n.length)==n)return{open:{pos:i-c,margin:c&&1},close:{pos:r+h,margin:h&&1}};r-i<=2*f?o=s=e.sliceDoc(i,r):(o=e.sliceDoc(i,i+f),s=e.sliceDoc(r-f,r));let d=/^\s*/.exec(o)[0].length,p=/\s*$/.exec(s)[0].length,m=s.length-p-n.length;return o.slice(d,d+t.length)==t&&s.slice(m,m+n.length)==n?{open:{pos:i+d+t.length,margin:/\s/.test(o.charAt(d+t.length))?1:0},close:{pos:r-p-n.length,margin:/\s/.test(s.charAt(m-1))?1:0}}:null}(t,i[n],e.from,e.to)));if(2!=e&&!r.every((e=>e)))return{changes:t.changes(n.map(((e,t)=>r[t]?[]:[{from:e.from,insert:i[t].open+" "},{from:e.to,insert:" "+i[t].close}])))};if(1!=e&&r.some((e=>e))){let e=[];for(let t,n=0;n<r.length;n++)if(t=r[n]){let r=i[n],{open:o,close:s}=t;e.push({from:o.pos-r.open.length,to:o.pos+o.margin},{from:s.pos-s.margin,to:s.pos+r.close.length})}return{changes:e}}return null}function p(e,t,n=t.selection.ranges){let i=[],r=-1;for(let{from:o,to:s}of n){let e=i.length,n=1e9,l=u(t,o).line;if(l){for(let e=o;e<=s;){let a=t.doc.lineAt(e);if(a.from>r&&(o==s||s>a.from)){r=a.from;let e=/^\s*/.exec(a.text)[0].length,t=e==a.length,o=a.text.slice(e,e+l.length)==l?e:-1;e<a.text.length&&e<n&&(n=e),i.push({line:a,comment:o,token:l,indent:e,empty:t,single:!1})}e=a.to+1}if(n<1e9)for(let t=e;t<i.length;t++)i[t].indent<i[t].line.text.length&&(i[t].indent=n);i.length==e+1&&(i[e].single=!0)}}if(2!=e&&i.some((e=>e.comment<0&&(!e.empty||e.single)))){let e=[];for(let{line:t,token:r,indent:o,empty:s,single:l}of i)!l&&s||e.push({from:t.from+o,insert:r+" "});let n=t.changes(e);return{changes:n,selection:t.selection.map(n,1)}}if(1!=e&&i.some((e=>e.comment>=0))){let e=[];for(let{line:t,comment:n,token:r}of i)if(n>=0){let i=t.from+n,o=i+r.length;" "==t.text[o-t.from]&&o++,e.push({from:i,to:o})}return{changes:e}}return null}const m=i.Annotation.define(),g=i.Annotation.define(),y=i.Facet.define(),v=i.Facet.define({combine:e=>(0,i.combineConfig)(e,{minDepth:100,newGroupDelay:500,joinToEvent:(e,t)=>t},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(e,t)=>(n,i)=>e(n,i)||t(n,i)})}),b=i.StateField.define({create:()=>L.empty,update(e,t){let n=t.state.facet(v),r=t.annotation(m);if(r){let i=E.fromTransaction(t,r.selection),o=r.side,s=0==o?e.undone:e.done;return s=i?O(s,s.length,n.minDepth,i):T(s,t.startState.selection),new L(0==o?r.rest:s,0==o?s:r.rest)}let o=t.annotation(g);if("full"!=o&&"before"!=o||(e=e.isolate()),!1===t.annotation(i.Transaction.addToHistory))return t.changes.empty?e:e.addMapping(t.changes.desc);let s=E.fromTransaction(t),l=t.annotation(i.Transaction.time),a=t.annotation(i.Transaction.userEvent);return s?e=e.addChanges(s,l,a,n,t):t.selection&&(e=e.addSelection(t.startState.selection,l,a,n.newGroupDelay)),"full"!=o&&"after"!=o||(e=e.isolate()),e},toJSON:e=>({done:e.done.map((e=>e.toJSON())),undone:e.undone.map((e=>e.toJSON()))}),fromJSON:e=>new L(e.done.map(E.fromJSON),e.undone.map(E.fromJSON))});function w(e={}){return[b,v.of(e),r.EditorView.domEventHandlers({beforeinput(e,t){let n="historyUndo"==e.inputType?k:"historyRedo"==e.inputType?S:null;return!!n&&(e.preventDefault(),n(t))}})]}function x(e,t){return function({state:n,dispatch:i}){if(!t&&n.readOnly)return!1;let r=n.field(b,!1);if(!r)return!1;let o=r.pop(e,n,t);return!!o&&(i(o),!0)}}const k=x(0,!1),S=x(1,!1),C=x(0,!0),A=x(1,!0);class E{constructor(e,t,n,i,r){this.changes=e,this.effects=t,this.mapped=n,this.startSelection=i,this.selectionsAfter=r}setSelAfter(e){return new E(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,n;return{changes:null===(e=this.changes)||void 0===e?void 0:e.toJSON(),mapped:null===(t=this.mapped)||void 0===t?void 0:t.toJSON(),startSelection:null===(n=this.startSelection)||void 0===n?void 0:n.toJSON(),selectionsAfter:this.selectionsAfter.map((e=>e.toJSON()))}}static fromJSON(e){return new E(e.changes&&i.ChangeSet.fromJSON(e.changes),[],e.mapped&&i.ChangeDesc.fromJSON(e.mapped),e.startSelection&&i.EditorSelection.fromJSON(e.startSelection),e.selectionsAfter.map(i.EditorSelection.fromJSON))}static fromTransaction(e,t){let n=M;for(let i of e.startState.facet(y)){let t=i(e);t.length&&(n=n.concat(t))}return!n.length&&e.changes.empty?null:new E(e.changes.invert(e.startState.doc),n,void 0,t||e.startState.selection,M)}static selection(e){return new E(void 0,M,void 0,void 0,e)}}function O(e,t,n,i){let r=t+1>n+20?t-n-1:0,o=e.slice(r,t);return o.push(i),o}function D(e,t){return e.length?t.length?e.concat(t):e:t}const M=[],P=200;function T(e,t){if(e.length){let n=e[e.length-1],i=n.selectionsAfter.slice(Math.max(0,n.selectionsAfter.length-P));return i.length&&i[i.length-1].eq(t)?e:(i.push(t),O(e,e.length-1,1e9,n.setSelAfter(i)))}return[E.selection([t])]}function I(e){let t=e[e.length-1],n=e.slice();return n[e.length-1]=t.setSelAfter(t.selectionsAfter.slice(0,t.selectionsAfter.length-1)),n}function B(e,t){if(!e.length)return e;let n=e.length,i=M;for(;n;){let r=N(e[n-1],t,i);if(r.changes&&!r.changes.empty||r.effects.length){let t=e.slice(0,n);return t[n-1]=r,t}t=r.mapped,n--,i=r.selectionsAfter}return i.length?[E.selection(i)]:M}function N(e,t,n){let r=D(e.selectionsAfter.length?e.selectionsAfter.map((e=>e.map(t))):M,n);if(!e.changes)return E.selection(r);let o=e.changes.map(t),s=t.mapDesc(e.changes,!0),l=e.mapped?e.mapped.composeDesc(s):s;return new E(o,i.StateEffect.mapEffects(e.effects,t),l,e.startSelection.map(s),r)}const R=/^(input\.type|delete)($|\.)/;class L{constructor(e,t,n=0,i=void 0){this.done=e,this.undone=t,this.prevTime=n,this.prevUserEvent=i}isolate(){return this.prevTime?new L(this.done,this.undone):this}addChanges(e,t,n,r,o){let s=this.done,l=s[s.length-1];return s=l&&l.changes&&!l.changes.empty&&e.changes&&(!n||R.test(n))&&(!l.selectionsAfter.length&&t-this.prevTime<r.newGroupDelay&&r.joinToEvent(o,function(e,t){let n=[],i=!1;return e.iterChangedRanges(((e,t)=>n.push(e,t))),t.iterChangedRanges(((e,t,r,o)=>{for(let s=0;s<n.length;){let e=n[s++],t=n[s++];o>=e&&r<=t&&(i=!0)}})),i}(l.changes,e.changes))||"input.type.compose"==n)?O(s,s.length-1,r.minDepth,new E(e.changes.compose(l.changes),D(i.StateEffect.mapEffects(e.effects,l.changes),l.effects),l.mapped,l.startSelection,M)):O(s,s.length,r.minDepth,e),new L(s,M,t,n)}addSelection(e,t,n,i){let r=this.done.length?this.done[this.done.length-1].selectionsAfter:M;return r.length>0&&t-this.prevTime<i&&n==this.prevUserEvent&&n&&/^select($|\.)/.test(n)&&(o=r[r.length-1],s=e,o.ranges.length==s.ranges.length&&0===o.ranges.filter(((e,t)=>e.empty!=s.ranges[t].empty)).length)?this:new L(T(this.done,e),this.undone,t,n);var o,s}addMapping(e){return new L(B(this.done,e),B(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,n){let i=0==e?this.done:this.undone;if(0==i.length)return null;let r=i[i.length-1],o=r.selectionsAfter[0]||t.selection;if(n&&r.selectionsAfter.length)return t.update({selection:r.selectionsAfter[r.selectionsAfter.length-1],annotations:m.of({side:e,rest:I(i),selection:o}),userEvent:0==e?"select.undo":"select.redo",scrollIntoView:!0});if(r.changes){let n=1==i.length?M:i.slice(0,i.length-1);return r.mapped&&(n=B(n,r.mapped)),t.update({changes:r.changes,selection:r.startSelection,effects:r.effects,annotations:m.of({side:e,rest:n,selection:o}),filter:!1,userEvent:0==e?"undo":"redo",scrollIntoView:!0})}return null}}L.empty=new L(M,M);const j=[{key:"Mod-z",run:k,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:S,preventDefault:!0},{linux:"Ctrl-Shift-z",run:S,preventDefault:!0},{key:"Mod-u",run:C,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:A,preventDefault:!0}];function F(e,t){return i.EditorSelection.create(e.ranges.map(t),e.mainIndex)}function W(e,t){return e.update({selection:t,scrollIntoView:!0,userEvent:"select"})}function V({state:e,dispatch:t},n){let i=F(e.selection,n);return!i.eq(e.selection,!0)&&(t(W(e,i)),!0)}function q(e,t){return i.EditorSelection.cursor(t?e.to:e.from)}function _(e,t){return V(e,(n=>n.empty?e.moveByChar(n,t):q(n,t)))}function z(e){return e.textDirectionAt(e.state.selection.main.head)==r.Direction.LTR}const U=e=>_(e,!z(e)),H=e=>_(e,z(e));function $(e,t){return V(e,(n=>n.empty?e.moveByGroup(n,t):q(n,t)))}"undefined"!=typeof Intl&&Intl.Segmenter;function J(e,t,n){if(t.type.prop(n))return!0;let i=t.to-t.from;return i&&(i>2||/[^\s,.;:]/.test(e.sliceDoc(t.from,t.to)))||t.firstChild}function K(e,t,n){let r,l,a=(0,o.mv)(e).resolveInner(t.head),c=n?s.uY.closedBy:s.uY.openedBy;for(let i=t.head;;){let t=n?a.childAfter(i):a.childBefore(i);if(!t)break;J(e,t,c)?a=t:i=n?t.to:t.from}return l=a.type.prop(c)&&(r=n?(0,o.jU)(e,a.from,1):(0,o.jU)(e,a.to,-1))&&r.matched?n?r.end.to:r.end.from:n?a.to:a.from,i.EditorSelection.cursor(l,n?-1:1)}function Y(e,t){return V(e,(n=>{if(!n.empty)return q(n,t);let i=e.moveVertically(n,t);return i.head!=n.head?i:e.moveToLineBoundary(n,t)}))}const G=e=>Y(e,!1),Z=e=>Y(e,!0);function Q(e){let t,n=e.scrollDOM.clientHeight<e.scrollDOM.scrollHeight-2,i=0,o=0;if(n){for(let t of e.state.facet(r.EditorView.scrollMargins)){let n=t(e);(null===n||void 0===n?void 0:n.top)&&(i=Math.max(null===n||void 0===n?void 0:n.top,i)),(null===n||void 0===n?void 0:n.bottom)&&(o=Math.max(null===n||void 0===n?void 0:n.bottom,o))}t=e.scrollDOM.clientHeight-i-o}else t=(e.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:i,marginBottom:o,selfScroll:n,height:Math.max(e.defaultLineHeight,t-5)}}function X(e,t){let n,i=Q(e),{state:o}=e,s=F(o.selection,(n=>n.empty?e.moveVertically(n,t,i.height):q(n,t)));if(s.eq(o.selection))return!1;if(i.selfScroll){let t=e.coordsAtPos(o.selection.main.head),l=e.scrollDOM.getBoundingClientRect(),a=l.top+i.marginTop,c=l.bottom-i.marginBottom;t&&t.top>a&&t.bottom<c&&(n=r.EditorView.scrollIntoView(s.main.head,{y:"start",yMargin:t.top-a}))}return e.dispatch(W(o,s),{effects:n}),!0}const ee=e=>X(e,!1),te=e=>X(e,!0);function ne(e,t,n){let r=e.lineBlockAt(t.head),o=e.moveToLineBoundary(t,n);if(o.head==t.head&&o.head!=(n?r.to:r.from)&&(o=e.moveToLineBoundary(t,n,!1)),!n&&o.head==r.from&&r.length){let n=/^\s*/.exec(e.state.sliceDoc(r.from,Math.min(r.from+100,r.to)))[0].length;n&&t.head!=r.from+n&&(o=i.EditorSelection.cursor(r.from+n))}return o}function ie(e,t,n){let r=!1,s=F(e.selection,(t=>{let s=(0,o.jU)(e,t.head,-1)||(0,o.jU)(e,t.head,1)||t.head>0&&(0,o.jU)(e,t.head-1,1)||t.head<e.doc.length&&(0,o.jU)(e,t.head+1,-1);if(!s||!s.end)return t;r=!0;let l=s.start.from==t.head?s.end.to:s.end.from;return n?i.EditorSelection.range(t.anchor,l):i.EditorSelection.cursor(l)}));return!!r&&(t(W(e,s)),!0)}function re(e,t){let n=F(e.state.selection,(e=>{let n=t(e);return i.EditorSelection.range(e.anchor,n.head,n.goalColumn,n.bidiLevel||void 0)}));return!n.eq(e.state.selection)&&(e.dispatch(W(e.state,n)),!0)}function oe(e,t){return re(e,(n=>e.moveByChar(n,t)))}const se=e=>oe(e,!z(e)),le=e=>oe(e,z(e));function ae(e,t){return re(e,(n=>e.moveByGroup(n,t)))}function ce(e,t){return re(e,(n=>e.moveVertically(n,t)))}const he=e=>ce(e,!1),ue=e=>ce(e,!0);function fe(e,t){return re(e,(n=>e.moveVertically(n,t,Q(e).height)))}const de=e=>fe(e,!1),pe=e=>fe(e,!0),me=({state:e,dispatch:t})=>(t(W(e,{anchor:0})),!0),ge=({state:e,dispatch:t})=>(t(W(e,{anchor:e.doc.length})),!0),ye=({state:e,dispatch:t})=>(t(W(e,{anchor:e.selection.main.anchor,head:0})),!0),ve=({state:e,dispatch:t})=>(t(W(e,{anchor:e.selection.main.anchor,head:e.doc.length})),!0);function be(e,t){if(e.state.readOnly)return!1;let n="delete.selection",{state:o}=e,s=o.changeByRange((r=>{let{from:o,to:s}=r;if(o==s){let i=t(r);i<o?(n="delete.backward",i=we(e,i,!1)):i>o&&(n="delete.forward",i=we(e,i,!0)),o=Math.min(o,i),s=Math.max(s,i)}else o=we(e,o,!1),s=we(e,s,!0);return o==s?{range:r}:{changes:{from:o,to:s},range:i.EditorSelection.cursor(o,o<r.head?-1:1)}}));return!s.changes.empty&&(e.dispatch(o.update(s,{scrollIntoView:!0,userEvent:n,effects:"delete.selection"==n?r.EditorView.announce.of(o.phrase("Selection deleted")):void 0})),!0)}function we(e,t,n){if(e instanceof r.EditorView)for(let i of e.state.facet(r.EditorView.atomicRanges).map((t=>t(e))))i.between(t,t,((e,i)=>{e<t&&i>t&&(t=n?i:e)}));return t}const xe=(e,t,n)=>be(e,(r=>{let s,l,a=r.from,{state:c}=e,h=c.doc.lineAt(a);if(n&&!t&&a>h.from&&a<h.from+200&&!/[^ \t]/.test(s=h.text.slice(0,a-h.from))){if("\t"==s[s.length-1])return a-1;let e=(0,i.countColumn)(s,c.tabSize)%(0,o.tp)(c)||(0,o.tp)(c);for(let t=0;t<e&&" "==s[s.length-1-t];t++)a--;l=a}else l=(0,i.findClusterBreak)(h.text,a-h.from,t,t)+h.from,l==a&&h.number!=(t?c.doc.lines:1)?l+=t?1:-1:!t&&/[\ufe00-\ufe0f]/.test(h.text.slice(l-h.from,a-h.from))&&(l=(0,i.findClusterBreak)(h.text,l-h.from,!1,!1)+h.from);return l})),ke=e=>xe(e,!1,!0),Se=e=>xe(e,!0,!1),Ce=(e,t)=>be(e,(n=>{let r=n.head,{state:o}=e,s=o.doc.lineAt(r),l=o.charCategorizer(r);for(let e=null;;){if(r==(t?s.to:s.from)){r==n.head&&s.number!=(t?o.doc.lines:1)&&(r+=t?1:-1);break}let a=(0,i.findClusterBreak)(s.text,r-s.from,t)+s.from,c=s.text.slice(Math.min(r,a)-s.from,Math.max(r,a)-s.from),h=l(c);if(null!=e&&h!=e)break;" "==c&&r==n.head||(e=h),r=a}return r})),Ae=e=>Ce(e,!1);function Ee(e){let t=[],n=-1;for(let i of e.selection.ranges){let r=e.doc.lineAt(i.from),o=e.doc.lineAt(i.to);if(i.empty||i.to!=o.from||(o=e.doc.lineAt(i.to-1)),n>=r.number){let e=t[t.length-1];e.to=o.to,e.ranges.push(i)}else t.push({from:r.from,to:o.to,ranges:[i]});n=o.number+1}return t}function Oe(e,t,n){if(e.readOnly)return!1;let r=[],o=[];for(let s of Ee(e)){if(n?s.to==e.doc.length:0==s.from)continue;let t=e.doc.lineAt(n?s.to+1:s.from-1),l=t.length+1;if(n){r.push({from:s.to,to:t.to},{from:s.from,insert:t.text+e.lineBreak});for(let t of s.ranges)o.push(i.EditorSelection.range(Math.min(e.doc.length,t.anchor+l),Math.min(e.doc.length,t.head+l)))}else{r.push({from:t.from,to:s.from},{from:s.to,insert:e.lineBreak+t.text});for(let e of s.ranges)o.push(i.EditorSelection.range(e.anchor-l,e.head-l))}}return!!r.length&&(t(e.update({changes:r,scrollIntoView:!0,selection:i.EditorSelection.create(o,e.selection.mainIndex),userEvent:"move.line"})),!0)}function De(e,t,n){if(e.readOnly)return!1;let i=[];for(let r of Ee(e))n?i.push({from:r.from,insert:e.doc.slice(r.from,r.to)+e.lineBreak}):i.push({from:r.to,insert:e.lineBreak+e.doc.slice(r.from,r.to)});return t(e.update({changes:i,scrollIntoView:!0,userEvent:"input.copyline"})),!0}const Me=Pe(!1);function Pe(e){return({state:t,dispatch:n})=>{if(t.readOnly)return!1;let r=t.changeByRange((n=>{let{from:r,to:l}=n,a=t.doc.lineAt(r),c=!e&&r==l&&function(e,t){if(/\(\)|\[\]|\{\}/.test(e.sliceDoc(t-1,t+1)))return{from:t,to:t};let n,i=(0,o.mv)(e).resolveInner(t),r=i.childBefore(t),l=i.childAfter(t);return r&&l&&r.to<=t&&l.from>=t&&(n=r.type.prop(s.uY.closedBy))&&n.indexOf(l.name)>-1&&e.doc.lineAt(r.to).from==e.doc.lineAt(l.from).from&&!/\S/.test(e.sliceDoc(r.to,l.from))?{from:r.to,to:l.from}:null}(t,r);e&&(r=l=(l<=a.to?a:t.doc.lineAt(l)).to);let h=new o.KB(t,{simulateBreak:r,simulateDoubleBreak:!!c}),u=(0,o._v)(h,r);for(null==u&&(u=(0,i.countColumn)(/^\s*/.exec(t.doc.lineAt(r).text)[0],t.tabSize));l<a.to&&/\s/.test(a.text[l-a.from]);)l++;c?({from:r,to:l}=c):r>a.from&&r<a.from+100&&!/\S/.test(a.text.slice(0,r))&&(r=a.from);let f=["",(0,o.EI)(t,u)];return c&&f.push((0,o.EI)(t,h.lineIndent(a.from,-1))),{changes:{from:r,to:l,insert:i.Text.of(f)},range:i.EditorSelection.cursor(r+1+f[1].length)}}));return n(t.update(r,{scrollIntoView:!0,userEvent:"input"})),!0}}function Te(e,t){let n=-1;return e.changeByRange((r=>{let o=[];for(let i=r.from;i<=r.to;){let s=e.doc.lineAt(i);s.number>n&&(r.empty||r.to>s.from)&&(t(s,o,r),n=s.number),i=s.to+1}let s=e.changes(o);return{changes:o,range:i.EditorSelection.range(s.mapPos(r.anchor,1),s.mapPos(r.head,1))}}))}const Ie=({state:e,dispatch:t})=>!e.readOnly&&(t(e.update(Te(e,((t,n)=>{n.push({from:t.from,insert:e.facet(o.Xt)})})),{userEvent:"input.indent"})),!0),Be=({state:e,dispatch:t})=>!e.readOnly&&(t(e.update(Te(e,((t,n)=>{let r=/^\s*/.exec(t.text)[0];if(!r)return;let s=(0,i.countColumn)(r,e.tabSize),l=0,a=(0,o.EI)(e,Math.max(0,s-(0,o.tp)(e)));for(;l<r.length&&l<a.length&&r.charCodeAt(l)==a.charCodeAt(l);)l++;n.push({from:t.from+l,to:t.from+r.length,insert:a.slice(l)})})),{userEvent:"delete.dedent"})),!0),Ne=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:e=>V(e,(t=>K(e.state,t,!z(e)))),shift:e=>re(e,(t=>K(e.state,t,!z(e))))},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:e=>V(e,(t=>K(e.state,t,z(e)))),shift:e=>re(e,(t=>K(e.state,t,z(e))))},{key:"Alt-ArrowUp",run:({state:e,dispatch:t})=>Oe(e,t,!1)},{key:"Shift-Alt-ArrowUp",run:({state:e,dispatch:t})=>De(e,t,!1)},{key:"Alt-ArrowDown",run:({state:e,dispatch:t})=>Oe(e,t,!0)},{key:"Shift-Alt-ArrowDown",run:({state:e,dispatch:t})=>De(e,t,!0)},{key:"Escape",run:({state:e,dispatch:t})=>{let n=e.selection,r=null;return n.ranges.length>1?r=i.EditorSelection.create([n.main]):n.main.empty||(r=i.EditorSelection.create([i.EditorSelection.cursor(n.main.head)])),!!r&&(t(W(e,r)),!0)}},{key:"Mod-Enter",run:Pe(!0)},{key:"Alt-l",mac:"Ctrl-l",run:({state:e,dispatch:t})=>{let n=Ee(e).map((({from:t,to:n})=>i.EditorSelection.range(t,Math.min(n+1,e.doc.length))));return t(e.update({selection:i.EditorSelection.create(n),userEvent:"select"})),!0}},{key:"Mod-i",run:({state:e,dispatch:t})=>{let n=F(e.selection,(t=>{let n=(0,o.mv)(e),r=n.resolveStack(t.from,1);if(t.empty){let e=n.resolveStack(t.from,-1);e.node.from>=r.node.from&&e.node.to<=r.node.to&&(r=e)}for(let e=r;e;e=e.next){let{node:n}=e;if((n.from<t.from&&n.to>=t.to||n.to>t.to&&n.from<=t.from)&&e.next)return i.EditorSelection.range(n.to,n.from)}return t}));return!n.eq(e.selection)&&(t(W(e,n)),!0)},preventDefault:!0},{key:"Mod-[",run:Be},{key:"Mod-]",run:Ie},{key:"Mod-Alt-\\",run:({state:e,dispatch:t})=>{if(e.readOnly)return!1;let n=Object.create(null),i=new o.KB(e,{overrideIndentation:e=>{let t=n[e];return null==t?-1:t}}),r=Te(e,((t,r,s)=>{let l=(0,o._v)(i,t.from);if(null==l)return;/\S/.test(t.text)||(l=0);let a=/^\s*/.exec(t.text)[0],c=(0,o.EI)(e,l);(a!=c||s.from<t.from+a.length)&&(n[t.from]=l,r.push({from:t.from,to:t.from+a.length,insert:c}))}));return r.changes.empty||t(e.update(r,{userEvent:"indent"})),!0}},{key:"Shift-Mod-k",run:e=>{if(e.state.readOnly)return!1;let{state:t}=e,n=t.changes(Ee(t).map((({from:e,to:n})=>(e>0?e--:n<t.doc.length&&n++,{from:e,to:n})))),i=F(t.selection,(t=>{let n;if(e.lineWrapping){let i=e.lineBlockAt(t.head),r=e.coordsAtPos(t.head,t.assoc||1);r&&(n=i.bottom+e.documentTop-r.bottom+e.defaultLineHeight/2)}return e.moveVertically(t,!0,n)})).map(n);return e.dispatch({changes:n,selection:i,scrollIntoView:!0,userEvent:"delete.line"}),!0}},{key:"Shift-Mod-\\",run:({state:e,dispatch:t})=>ie(e,t,!1)},{key:"Mod-/",run:e=>{let{state:t}=e,n=t.doc.lineAt(t.selection.main.from),i=u(e.state,n.from);return i.line?a(e):!!i.block&&h(e)}},{key:"Alt-A",run:c},{key:"Ctrl-m",mac:"Shift-Alt-m",run:e=>(e.setTabFocusMode(),!0)}].concat([{key:"ArrowLeft",run:U,shift:se,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:e=>$(e,!z(e)),shift:e=>ae(e,!z(e)),preventDefault:!0},{mac:"Cmd-ArrowLeft",run:e=>V(e,(t=>ne(e,t,!z(e)))),shift:e=>re(e,(t=>ne(e,t,!z(e)))),preventDefault:!0},{key:"ArrowRight",run:H,shift:le,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:e=>$(e,z(e)),shift:e=>ae(e,z(e)),preventDefault:!0},{mac:"Cmd-ArrowRight",run:e=>V(e,(t=>ne(e,t,z(e)))),shift:e=>re(e,(t=>ne(e,t,z(e)))),preventDefault:!0},{key:"ArrowUp",run:G,shift:he,preventDefault:!0},{mac:"Cmd-ArrowUp",run:me,shift:ye},{mac:"Ctrl-ArrowUp",run:ee,shift:de},{key:"ArrowDown",run:Z,shift:ue,preventDefault:!0},{mac:"Cmd-ArrowDown",run:ge,shift:ve},{mac:"Ctrl-ArrowDown",run:te,shift:pe},{key:"PageUp",run:ee,shift:de},{key:"PageDown",run:te,shift:pe},{key:"Home",run:e=>V(e,(t=>ne(e,t,!1))),shift:e=>re(e,(t=>ne(e,t,!1))),preventDefault:!0},{key:"Mod-Home",run:me,shift:ye},{key:"End",run:e=>V(e,(t=>ne(e,t,!0))),shift:e=>re(e,(t=>ne(e,t,!0))),preventDefault:!0},{key:"Mod-End",run:ge,shift:ve},{key:"Enter",run:Me,shift:Me},{key:"Mod-a",run:({state:e,dispatch:t})=>(t(e.update({selection:{anchor:0,head:e.doc.length},userEvent:"select"})),!0)},{key:"Backspace",run:ke,shift:ke},{key:"Delete",run:Se},{key:"Mod-Backspace",mac:"Alt-Backspace",run:Ae},{key:"Mod-Delete",mac:"Alt-Delete",run:e=>Ce(e,!0)},{mac:"Mod-Backspace",run:e=>be(e,(t=>{let n=e.moveToLineBoundary(t,!1).head;return t.head>n?n:Math.max(0,t.head-1)}))},{mac:"Mod-Delete",run:e=>be(e,(t=>{let n=e.moveToLineBoundary(t,!0).head;return t.head<n?n:Math.min(e.state.doc.length,t.head+1)}))}].concat([{key:"Ctrl-b",run:U,shift:se,preventDefault:!0},{key:"Ctrl-f",run:H,shift:le},{key:"Ctrl-p",run:G,shift:he},{key:"Ctrl-n",run:Z,shift:ue},{key:"Ctrl-a",run:e=>V(e,(t=>i.EditorSelection.cursor(e.lineBlockAt(t.head).from,1))),shift:e=>re(e,(t=>i.EditorSelection.cursor(e.lineBlockAt(t.head).from)))},{key:"Ctrl-e",run:e=>V(e,(t=>i.EditorSelection.cursor(e.lineBlockAt(t.head).to,-1))),shift:e=>re(e,(t=>i.EditorSelection.cursor(e.lineBlockAt(t.head).to)))},{key:"Ctrl-d",run:Se},{key:"Ctrl-h",run:ke},{key:"Ctrl-k",run:e=>be(e,(t=>{let n=e.lineBlockAt(t.head).to;return t.head<n?n:Math.min(e.state.doc.length,t.head+1)}))},{key:"Ctrl-Alt-h",run:Ae},{key:"Ctrl-o",run:({state:e,dispatch:t})=>{if(e.readOnly)return!1;let n=e.changeByRange((e=>({changes:{from:e.from,to:e.to,insert:i.Text.of(["",""])},range:i.EditorSelection.cursor(e.from)})));return t(e.update(n,{scrollIntoView:!0,userEvent:"input"})),!0}},{key:"Ctrl-t",run:({state:e,dispatch:t})=>{if(e.readOnly)return!1;let n=e.changeByRange((t=>{if(!t.empty||0==t.from||t.from==e.doc.length)return{range:t};let n=t.from,r=e.doc.lineAt(n),o=n==r.from?n-1:(0,i.findClusterBreak)(r.text,n-r.from,!1)+r.from,s=n==r.to?n+1:(0,i.findClusterBreak)(r.text,n-r.from,!0)+r.from;return{changes:{from:o,to:s,insert:e.doc.slice(n,s).append(e.doc.slice(o,n))},range:i.EditorSelection.cursor(s)}}));return!n.changes.empty&&(t(e.update(n,{scrollIntoView:!0,userEvent:"move.character"})),!0)}},{key:"Ctrl-v",run:te}].map((e=>({mac:e.key,run:e.run,shift:e.shift}))))),Re={key:"Tab",run:Ie,shift:Be}},730:e=>{e.exports=r},742:e=>{e.exports=t},806:(e,t,n)=>{n.d(t,{c:()=>r});var i=n(730),r=i.EditorView.theme({"&":{backgroundColor:"#fff"}},{dark:!1})},957:(e,t,n)=>{function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function r(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}function o(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){o(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}n.d(t,{A:()=>l})}},s={};function l(e){var t=s[e];if(void 0!==t)return t.exports;var n=s[e]={exports:{}};return o[e](n,n.exports,l),n.exports}l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},l.d=(e,t)=>{for(var n in t)l.o(t,n)&&!l.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),l.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};l.r(a),l.d(a,{basicSetup:()=>y.o,default:()=>k,getStatistics:()=>b.m,minimalSetup:()=>y.V,useCodeMirror:()=>f.q});var c=l(957),h=l(644),u=l(442),f=l(695),d=l(742),p=l(730),m={};for(const e in p)"default"!==e&&(m[e]=()=>p[e]);l.d(a,m);var g=l(60);m={};for(const e in g)"default"!==e&&(m[e]=()=>g[e]);l.d(a,m);var y=l(687),v=l(89);m={};for(const e in v)["default","basicSetup","minimalSetup","useCodeMirror"].indexOf(e)<0&&(m[e]=()=>v[e]);l.d(a,m);var b=l(369),w=["className","value","selection","extensions","onChange","onStatistics","onCreateEditor","onUpdate","autoFocus","theme","height","minHeight","maxHeight","width","minWidth","maxWidth","basicSetup","placeholder","indentWithTab","editable","readOnly","root","initialState"],x=(0,u.forwardRef)((function(e,t){var n=e.className,i=e.value,r=void 0===i?"":i,o=e.selection,s=e.extensions,l=void 0===s?[]:s,a=e.onChange,p=e.onStatistics,m=e.onCreateEditor,g=e.onUpdate,y=e.autoFocus,v=e.theme,b=void 0===v?"light":v,x=e.height,k=e.minHeight,S=e.maxHeight,C=e.width,A=e.minWidth,E=e.maxWidth,O=e.basicSetup,D=e.placeholder,M=e.indentWithTab,P=e.editable,T=e.readOnly,I=e.root,B=e.initialState,N=(0,h.A)(e,w),R=(0,u.useRef)(null),L=(0,f.q)({root:I,value:r,autoFocus:y,theme:b,height:x,minHeight:k,maxHeight:S,width:C,minWidth:A,maxWidth:E,basicSetup:O,placeholder:D,indentWithTab:M,editable:P,readOnly:T,selection:o,onChange:a,onStatistics:p,onCreateEditor:m,onUpdate:g,extensions:l,initialState:B}),j=L.state,F=L.view,W=L.container,V=L.setContainer;(0,u.useImperativeHandle)(t,(function(){return{editor:R.current,state:j,view:F}}),[R,W,j,F]);var q=(0,u.useCallback)((function(e){R.current=e,V(e)}),[V]);if("string"!==typeof r)throw new Error("value must be typeof string but got ".concat(typeof r));var _="string"===typeof b?"cm-theme-".concat(b):"cm-theme";return(0,d.jsx)("div",(0,c.A)({ref:q,className:"".concat(_).concat(n?" ".concat(n):"")},N))}));x.displayName="CodeMirror";const k=x;return a})()));