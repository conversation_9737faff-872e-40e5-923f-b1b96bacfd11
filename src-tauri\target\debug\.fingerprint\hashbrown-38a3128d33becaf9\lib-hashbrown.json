{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 15657897354478470176, "path": 11035767788352647301, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-38a3128d33becaf9\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}