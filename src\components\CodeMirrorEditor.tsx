import React, { useCallback, useMemo } from 'react'
import CodeMirror from '@uiw/react-codemirror'
import { EditorView, Decoration, DecorationSet, ViewPlugin, ViewUpdate } from '@codemirror/view'
import { Extension, RangeSetBuilder } from '@codemirror/state'
import { ColorTheme, AppSettings } from '../types'

interface CodeMirrorEditorProps {
  value: string
  onChange: (value: string) => void
  theme: ColorTheme
  settings: AppSettings
  placeholder?: string
  onKeyDown?: (event: KeyboardEvent) => boolean
}

const CodeMirrorEditor: React.FC<CodeMirrorEditorProps> = ({
  value,
  onChange,
  theme,
  settings,
  placeholder = "开始写作...",
  onKeyDown
}) => {
  // 创建语法高亮装饰器
  const syntaxHighlightPlugin = useMemo(() => {
    // 定义装饰器样式
    const chineseMark = Decoration.mark({
      style: `color: ${theme.colors.chinese}; font-weight: normal;`
    })
    const englishMark = Decoration.mark({
      style: `color: ${theme.colors.english}; font-weight: normal;`
    })
    const numberMark = Decoration.mark({
      style: `color: ${theme.colors.number}; font-weight: bold;`
    })
    const punctuationMark = Decoration.mark({
      style: `color: ${theme.colors.punctuation}; font-weight: normal;`
    })

    // 创建视图插件
    return ViewPlugin.fromClass(
      class {
        decorations: DecorationSet

        constructor(view: any) {
          this.decorations = this.buildDecorations(view)
        }

        update(update: ViewUpdate) {
          if (update.docChanged || update.viewportChanged) {
            this.decorations = this.buildDecorations(update.view)
          }
        }

        buildDecorations(view: any) {
          const builder = new RangeSetBuilder<Decoration>()
          const doc = view.state.doc
          const text = doc.toString()

          let i = 0
          while (i < text.length) {
            const char = text[i]
            let decoration = null
            let length = 1

            // 检测连续的相同类型字符
            if (/[0-9]/.test(char)) {
              // 数字
              decoration = numberMark
              while (i + length < text.length && /[0-9]/.test(text[i + length])) {
                length++
              }
            } else if (/[a-zA-Z]/.test(char)) {
              // 英文
              decoration = englishMark
              while (i + length < text.length && /[a-zA-Z]/.test(text[i + length])) {
                length++
              }
            } else if (/[\u4e00-\u9fff]/.test(char)) {
              // 汉字
              decoration = chineseMark
              while (i + length < text.length && /[\u4e00-\u9fff]/.test(text[i + length])) {
                length++
              }
            } else if (!/\s/.test(char)) {
              // 标点符号
              decoration = punctuationMark
              while (i + length < text.length && !/\s/.test(text[i + length]) &&
                     !/[0-9a-zA-Z\u4e00-\u9fff]/.test(text[i + length])) {
                length++
              }
            }

            if (decoration) {
              builder.add(i, i + length, decoration)
            }

            i += length
          }

          return builder.finish()
        }
      },
      {
        decorations: (v) => v.decorations
      }
    )
  }, [theme])

  // 编辑器扩展配置
  const extensions = useMemo(() => {
    const exts: Extension[] = [
      // 语法高亮插件
      syntaxHighlightPlugin,
      
      // 编辑器主题
      EditorView.theme({
        '&': {
          fontSize: `${settings.fontSize}px`,
          fontFamily: '"Consolas", "Monaco", "Courier New", monospace',
          backgroundColor: theme.colors.background,
          color: theme.colors.text,
          height: '100%'
        },
        '.cm-content': {
          padding: '16px',
          minHeight: '100%',
          lineHeight: '1.7',
          caretColor: theme.colors.text
        },
        '.cm-focused': {
          outline: 'none'
        },
        '.cm-editor': {
          height: '100%'
        },
        '.cm-scroller': {
          height: '100%'
        },
        '.cm-gutters': {
          backgroundColor: '#f8f9fa',
          borderRight: '1px solid #e9ecef',
          color: '#6c757d',
          fontSize: `${settings.fontSize - 2}px`
        },
        '.cm-lineNumbers': {
          minWidth: '40px',
          paddingRight: '8px'
        },
        '.cm-activeLineGutter': {
          backgroundColor: '#e3f2fd'
        },
        '.cm-activeLine': {
          backgroundColor: 'rgba(59, 130, 246, 0.05)'
        },
        '.cm-selectionBackground': {
          backgroundColor: 'rgba(59, 130, 246, 0.2)'
        },
        '.cm-cursor': {
          borderLeftColor: theme.colors.text,
          borderLeftWidth: '2px'
        }
      }),
      
      // 行号显示
      ...(settings.showLineNumbers ? [EditorView.lineNumbers()] : []),
      
      // 自动换行
      EditorView.lineWrapping,
      
      // 键盘事件处理
      EditorView.domEventHandlers({
        keydown: (event) => {
          if (onKeyDown) {
            return onKeyDown(event)
          }
          return false
        }
      })
    ]
    
    return exts
  }, [syntaxHighlightPlugin, theme, settings, onKeyDown])

  // 处理内容变化
  const handleChange = useCallback((val: string) => {
    onChange(val)
  }, [onChange])

  return (
    <div className="h-full w-full">
      <CodeMirror
        value={value}
        onChange={handleChange}
        extensions={extensions}
        placeholder={placeholder}
        basicSetup={{
          lineNumbers: false, // 我们用自己的行号配置
          foldGutter: false,
          dropCursor: false,
          allowMultipleSelections: false,
          indentOnInput: true,
          bracketMatching: false,
          closeBrackets: false,
          autocompletion: false,
          highlightSelectionMatches: false,
          searchKeymap: false
        }}
      />
    </div>
  )
}

export default CodeMirrorEditor
