import React, { useState } from 'react'
import { Note } from '../types'

interface SidebarProps {
  notes: Note[]
  activeNoteId: string | null
  searchTerm: string
  onSearchChange: (term: string) => void
  onNoteSelect: (id: string) => void
  onNewNote: () => void
  onDeleteNote: (id: string) => void
  onUpdateNote: (id: string, updates: Partial<Note>) => void
  onShowSettings: () => void
}

const Sidebar: React.FC<SidebarProps> = ({
  notes,
  activeNoteId,
  searchTerm,
  onSearchChange,
  onNoteSelect,
  onNewNote,
  onDeleteNote,
  onUpdateNote,
  onShowSettings
}) => {
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editingTitle, setEditingTitle] = useState('')

  const handleTitleEdit = (note: Note) => {
    setEditingId(note.id)
    setEditingTitle(note.title)
  }

  const handleTitleSave = (id: string) => {
    if (editingTitle.trim()) {
      onUpdateNote(id, { title: editingTitle.trim() })
    }
    setEditingId(null)
    setEditingTitle('')
  }

  const handleTitleCancel = () => {
    setEditingId(null)
    setEditingTitle('')
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return '今天'
    } else if (diffDays === 2) {
      return '昨天'
    } else if (diffDays <= 7) {
      return `${diffDays - 1}天前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  const getPreview = (content: string) => {
    return content.slice(0, 50) + (content.length > 50 ? '...' : '')
  }

  return (
    <div className="w-72 bg-white/80 backdrop-blur-sm border-r border-gray-200/50 flex flex-col shadow-sm">
      {/* 头部 */}
      <div className="px-4 py-3 border-b border-gray-200/50 bg-gradient-to-r from-slate-50 to-gray-50">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <h1 className="text-sm font-semibold text-gray-800">记事本</h1>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={onShowSettings}
              className="p-1.5 text-gray-500 hover:text-white hover:bg-gradient-to-r hover:from-gray-500 hover:to-gray-600 rounded-lg transition-all duration-200 hover:shadow-md"
              title="设置"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
            <button
              onClick={onNewNote}
              className="p-1.5 text-gray-500 hover:text-white hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-md"
              title="新建笔记"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <input
            type="text"
            placeholder="搜索笔记..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full pl-8 pr-3 py-2 text-sm bg-white/70 border border-gray-200/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 focus:bg-white transition-all duration-200 placeholder-gray-400"
          />
          <svg
            className="absolute left-2.5 top-2.5 w-3.5 h-3.5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* 笔记列表 */}
      <div className="flex-1 overflow-y-auto bg-gradient-to-b from-transparent to-gray-50/30">
        {notes.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <p className="text-sm font-medium mb-1">还没有笔记</p>
            <p className="text-xs text-gray-400">点击上方 + 号创建第一个笔记</p>
          </div>
        ) : (
          <div className="space-y-1 p-3">
            {notes.map((note) => (
              <div
                key={note.id}
                className={`group relative p-3 rounded-xl cursor-pointer transition-all duration-200 ${
                  activeNoteId === note.id
                    ? 'bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200/50 shadow-sm'
                    : 'hover:bg-white/80 hover:shadow-sm hover:border hover:border-gray-200/50'
                }`}
                onClick={() => onNoteSelect(note.id)}
              >
                {/* 标题 */}
                {editingId === note.id ? (
                  <input
                    type="text"
                    value={editingTitle}
                    onChange={(e) => setEditingTitle(e.target.value)}
                    onBlur={() => handleTitleSave(note.id)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleTitleSave(note.id)
                      } else if (e.key === 'Escape') {
                        handleTitleCancel()
                      }
                    }}
                    className="w-full text-sm font-medium text-gray-800 bg-transparent border-none outline-none focus:ring-0"
                    autoFocus
                    onClick={(e) => e.stopPropagation()}
                  />
                ) : (
                  <h3 className="text-sm font-medium text-gray-800 truncate mb-1.5">
                    {note.title}
                  </h3>
                )}

                {/* 内容预览 */}
                <p className="text-xs text-gray-500 mb-2 line-clamp-2 leading-relaxed">
                  {getPreview(note.content) || '空白笔记'}
                </p>

                {/* 日期和操作按钮 */}
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-400 font-medium">
                    {formatDate(note.updatedAt)}
                  </span>

                  <div className="opacity-0 group-hover:opacity-100 transition-all duration-200 flex space-x-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleTitleEdit(note)
                      }}
                      className="p-1 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-md transition-all duration-200"
                      title="重命名"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        if (confirm('确定要删除这个笔记吗？')) {
                          onDeleteNote(note.id)
                        }
                      }}
                      className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-md transition-all duration-200"
                      title="删除"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部信息 */}
      <div className="px-4 py-2 border-t border-gray-200/50 bg-gradient-to-r from-slate-50 to-gray-50">
        <div className="flex items-center justify-center space-x-2 text-xs text-gray-400">
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span>共 {notes.length} 个笔记</span>
        </div>
      </div>
    </div>
  )
}

export default Sidebar
