{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"small_rng\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2225463790103693989, "path": 12401347001645290063, "deps": [[1573238666360410412, "rand_chacha", false, 5829690419357659745], [18130209639506977569, "rand_core", false, 18365067737995669744]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-7f070f07ae77767a\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}